{"name": "icaldz-pos", "private": true, "version": "1.0.0", "description": "iCalDZ Accounting System", "author": "iCode DZ <<EMAIL>>", "main": "main.js", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dist": "npm run build && electron-builder --publish=never", "pack": "npm run build && electron-builder --dir"}, "dependencies": {"cors": "^2.8.5", "crypto-js": "^4.2.0", "express": "^4.18.2", "jsbarcode": "^3.11.5", "jsqrcode": "^1.0.0", "lucide-react": "^0.263.1", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "concurrently": "^7.6.0", "electron": "^22.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "terser": "^5.40.0", "vite": "^4.4.5", "wait-on": "^7.0.1"}, "build": {"appId": "com.icodedz.accounting", "productName": "iCalDZ Accounting System", "directories": {"output": "../iCalDZ-Installer"}, "files": ["dist/**/*", "main.js", "package.json"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}, {"from": "public/", "to": "public/", "filter": ["*.png", "*.svg"]}], "win": {"target": "nsis", "icon": "assets/logo2png.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}