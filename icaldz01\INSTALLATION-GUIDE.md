# 🚀 دليل التثبيت والإعداد - نظام iCalDZ
# iCalDZ Installation & Setup Guide

## 📋 نظرة عامة / Overview

هذا الدليل يوضح كيفية تثبيت وإعداد نظام iCalDZ المحاسبي على أنظمة التشغيل المختلفة.

This guide explains how to install and set up the iCalDZ accounting system on different operating systems.

## 🔧 متطلبات النظام / System Requirements

### الحد الأدنى / Minimum Requirements
- **💻 نظام التشغيل / OS**: Windows 10, macOS 10.14, Ubuntu 18.04
- **💾 الذاكرة / RAM**: 4GB
- **💿 مساحة القرص / Storage**: 500MB
- **🌐 المتصفح / Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### الموصى به / Recommended
- **💻 نظام التشغيل / OS**: Windows 11, macOS 12+, Ubuntu 20.04+
- **💾 الذاكرة / RAM**: 8GB أو أكثر
- **💿 مساحة القرص / Storage**: 2GB مساحة فارغة
- **🌐 المتصفح / Browser**: أحدث إصدار

## 📦 طرق التثبيت / Installation Methods

### 1️⃣ التثبيت السريع (مستحسن) / Quick Installation (Recommended)

#### أ. تحميل الملفات / Download Files
```bash
# استنساخ المشروع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# أو تحميل ZIP وفك الضغط
# Download ZIP and extract
```

#### ب. تثبيت المتطلبات / Install Dependencies
```bash
# تثبيت Node.js (إذا لم يكن مثبتاً)
# Install Node.js (if not installed)
# تحميل من: https://nodejs.org

# تثبيت المكتبات
npm install
```

#### ج. تشغيل التطبيق / Run Application
```bash
# تشغيل سريع باستخدام ملف الدفع
start-app.bat

# أو تشغيل يدوي
npm run electron-dev
```

### 2️⃣ التثبيت المتقدم / Advanced Installation

#### أ. إعداد بيئة التطوير / Development Environment Setup
```bash
# تثبيت Node.js و npm
# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تحقق من الإصدارات
node --version  # يجب أن يكون 18.0.0 أو أحدث
npm --version   # يجب أن يكون 8.0.0 أو أحدث
```

#### ب. تثبيت المشروع / Project Installation
```bash
# استنساخ المشروع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# تثبيت جميع المتطلبات
npm install

# تثبيت متطلبات إضافية (اختياري)
npm install -g electron
npm install -g concurrently
```

#### ج. بناء التطبيق / Build Application
```bash
# بناء للتطوير
npm run build

# بناء ملف قابل للتشغيل
npm run dist

# بناء لجميع المنصات
npm run build:all
```

## 🖥️ التثبيت حسب نظام التشغيل / OS-Specific Installation

### 🪟 Windows

#### الطريقة الأولى: ملف EXE (الأسهل)
1. **تحميل الملف**: احصل على `iCalDZ-Setup.exe`
2. **تشغيل المثبت**: انقر نقراً مزدوجاً على الملف
3. **اتباع التعليمات**: اتبع معالج التثبيت
4. **التفعيل**: أدخل كود التفعيل عند التشغيل الأول

#### الطريقة الثانية: من المصدر
```cmd
# فتح Command Prompt كمدير
# Open Command Prompt as Administrator

# تثبيت Node.js
# Download from: https://nodejs.org/en/download/

# استنساخ المشروع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm run electron-dev
```

#### ملفات الدفع المساعدة / Helper Batch Files
```cmd
# تشغيل سريع
start-app.bat

# بناء التطبيق
build-app.bat

# توليد أكواد التفعيل
generate-code-en.bat
```

### 🍎 macOS

#### التثبيت عبر Terminal
```bash
# تثبيت Homebrew (إذا لم يكن مثبتاً)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت Node.js
brew install node

# استنساخ المشروع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm run electron-dev
```

#### إنشاء ملف DMG
```bash
# بناء تطبيق macOS
npm run build:mac

# إنشاء ملف DMG للتوزيع
npm run dist:mac
```

### 🐧 Linux (Ubuntu/Debian)

#### التثبيت عبر Terminal
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت Git (إذا لم يكن مثبتاً)
sudo apt install git -y

# استنساخ المشروع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm run electron-dev
```

#### إنشاء حزمة AppImage
```bash
# بناء تطبيق Linux
npm run build:linux

# إنشاء ملف AppImage
npm run dist:linux
```

## 🔐 إعداد التفعيل / Activation Setup

### 1️⃣ الحصول على كود التفعيل / Getting Activation Code

#### أكواد التجربة / Trial Codes
```bash
# توليد كود تجربة يوم واحد
node generate-trial-codes.js 1day "اسم العميل"

# توليد كود تجربة 7 أيام
node generate-trial-codes.js 7days "اسم العميل"

# توليد كود تجربة مخصص (30 يوم)
node generate-trial-codes.js custom "اسم العميل" 30
```

#### كود التفعيل الدائم / Lifetime Activation
```bash
# توليد كود تفعيل دائم
node generate-trial-codes.js lifetime "اسم العميل"

# توليد كود مع معرف الجهاز
node generate-activation-code-en.js --client "اسم العميل" --machine "معرف الجهاز"
```

### 2️⃣ تفعيل النظام / System Activation

1. **تشغيل التطبيق**: افتح iCalDZ لأول مرة
2. **نافذة التفعيل**: ستظهر نافذة طلب التفعيل
3. **إدخال الكود**: أدخل كود التفعيل المستلم
4. **التحقق**: انقر على "تفعيل" للتحقق من الكود
5. **التأكيد**: ستظهر رسالة تأكيد نجاح التفعيل

### 3️⃣ استكشاف أخطاء التفعيل / Activation Troubleshooting

#### مشاكل شائعة / Common Issues
- **كود غير صحيح**: تأكد من إدخال الكود بشكل صحيح
- **كود منتهي الصلاحية**: تحقق من تاريخ انتهاء الكود
- **كود مستخدم**: كل كود يُستخدم مرة واحدة فقط
- **مشكلة الجهاز**: الكود مرتبط بجهاز محدد

#### حلول / Solutions
```bash
# التحقق من صحة الكود
node generate-activation-code-en.js --validate "الكود المراد التحقق منه"

# إعادة تعيين التفعيل (للمطورين فقط)
localStorage.removeItem('icaldz-activation-data');
```

## ⚙️ الإعداد الأولي / Initial Configuration

### 1️⃣ اختيار اللغة / Language Selection
- **العربية**: للمستخدمين العرب (RTL)
- **الفرنسية**: للمستخدمين الفرنسيين (LTR)
- **الإنجليزية**: للمستخدمين الدوليين (LTR)

### 2️⃣ إعدادات الشركة / Company Settings
```javascript
// إعدادات أساسية
const storeSettings = {
  storeName: "اسم المتجر",
  storePhone: "+213 XXX XXX XXX",
  storeAddress: "عنوان المتجر",
  taxRate: 19, // معدل الضريبة %
  currency: "DZD" // العملة
};
```

### 3️⃣ إعداد قاعدة البيانات / Database Setup
```bash
# إنشاء بيانات تجريبية (اختياري)
npm run seed:demo

# استيراد بيانات موجودة
# استخدم ميزة "استيراد من Excel" في الواجهة
```

## 🔧 إعدادات متقدمة / Advanced Configuration

### 1️⃣ إعداد الخادم / Server Configuration
```javascript
// config.js
export const appConfig = {
  server: {
    port: 3000,
    host: 'localhost'
  },
  database: {
    type: 'localStorage',
    backup: true,
    syncInterval: 300000 // 5 دقائق
  },
  security: {
    encryption: true,
    sessionTimeout: 3600000 // ساعة واحدة
  }
};
```

### 2️⃣ إعداد الطباعة / Printing Configuration
```javascript
// إعدادات الطابعة الحرارية
const thermalPrinter = {
  width: 80, // mm
  characterSet: 'UTF-8',
  codePage: 'CP864', // للعربية
  autocut: true
};
```

### 3️⃣ إعداد النسخ الاحتياطية / Backup Configuration
```javascript
// إعداد النسخ الاحتياطية التلقائية
const backupConfig = {
  enabled: true,
  interval: 'daily', // يومي
  location: './backups/',
  retention: 30, // الاحتفاظ لـ 30 يوم
  compression: true
};
```

## 🚀 تشغيل التطبيق / Running the Application

### 1️⃣ وضع التطوير / Development Mode
```bash
# تشغيل خادم التطوير
npm run dev

# تشغيل Electron في وضع التطوير
npm run electron-dev

# تشغيل مع إعادة التحميل التلقائي
npm run dev:watch
```

### 2️⃣ وضع الإنتاج / Production Mode
```bash
# بناء للإنتاج
npm run build

# تشغيل النسخة المبنية
npm run electron

# إنشاء ملف قابل للتشغيل
npm run dist
```

### 3️⃣ استخدام ملفات الدفع / Using Batch Files
```cmd
# Windows - تشغيل سريع
start-app.bat

# اختيار وضع التشغيل
1. تطوير (Development)
2. إنتاج (Production) 
3. Electron
4. بناء ملف قابل للتشغيل
5. تثبيت المكتبات
```

## 🔍 استكشاف الأخطاء / Troubleshooting

### مشاكل التثبيت / Installation Issues

#### خطأ في تثبيت المكتبات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install

# تثبيت بصلاحيات مدير (Windows)
npm install --force

# تثبيت بدون cache
npm install --no-cache
```

#### مشاكل الأذونات / Permission Issues
```bash
# Linux/macOS - إصلاح الأذونات
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) ./node_modules

# Windows - تشغيل كمدير
# Run Command Prompt as Administrator
```

### مشاكل التشغيل / Runtime Issues

#### التطبيق لا يبدأ / Application Won't Start
1. **تحقق من Node.js**: `node --version`
2. **تحقق من المكتبات**: `npm list`
3. **مسح الذاكرة المؤقتة**: `npm cache clean --force`
4. **إعادة تثبيت**: `npm install`

#### مشاكل الأداء / Performance Issues
1. **إغلاق التطبيقات الأخرى**: لتوفير الذاكرة
2. **تحديث المتصفح**: استخدم أحدث إصدار
3. **مسح بيانات المتصفح**: مسح cache و localStorage
4. **إعادة تشغيل النظام**: في حالة المشاكل المستمرة

## 📞 الدعم الفني / Technical Support

### معلومات الاتصال / Contact Information
- **📧 البريد الإلكتروني**: <EMAIL>
- **📱 الهاتف**: +213 XXX XXX XXX
- **🌐 الموقع**: www.icodedz.com
- **💬 الدردشة**: متوفرة على الموقع

### ساعات العمل / Working Hours
- **🕐 الأحد - الخميس**: 8:00 - 17:00 (توقيت الجزائر)
- **🕐 الجمعة**: 8:00 - 12:00 (توقيت الجزائر)
- **🚫 السبت**: مغلق

---

**© 2025 iCode DZ - جميع الحقوق محفوظة / All Rights Reserved**
