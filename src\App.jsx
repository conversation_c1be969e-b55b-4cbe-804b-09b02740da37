import React from 'react';
import { LanguageProvider } from './LanguageContext.jsx';
import { AppStateProvider } from './contexts/AppStateContext.jsx';
import AppRouter from './components/AppRouter.jsx';
import './index.css';

// Main App component - Clean architecture
const App = () => {
  return (
    <LanguageProvider>
      <AppStateProvider>
        <AppRouter />
      </AppStateProvider>
    </LanguageProvider>
  );
};

export default App;
