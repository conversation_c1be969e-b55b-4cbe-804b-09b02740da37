export const categories = [
  {
    id: 'food',
    name: {
      ar: 'طعام',
      en: 'Food',
      fr: 'Nourriture'
    },
    icon: '🍕'
  },
  {
    id: 'clothing',
    name: {
      ar: 'ملابس',
      en: 'Clothing',
      fr: 'Vêtements'
    },
    icon: '👕'
  },
  {
    id: 'home',
    name: {
      ar: 'أدوات منزلية',
      en: 'Home & Garden',
      fr: 'Maison & Jardin'
    },
    icon: '🏠'
  },
  {
    id: 'electronics',
    name: {
      ar: 'إلكترونيات',
      en: 'Electronics',
      fr: 'Électronique'
    },
    icon: '📱'
  }
];

export const products = [
  // Food Category
  {
    id: 1,
    name: {
      ar: 'بيتزا مارجريتا',
      en: 'Margherita Pizza',
      fr: 'Pizza Margherita'
    },
    price: 1200,
    category: 'food',
    icon: '🍕',
    image: '/images/pizza.jpg'
  },
  {
    id: 2,
    name: {
      ar: 'برجر لحم',
      en: 'Beef Burger',
      fr: 'Burger de Bœuf'
    },
    price: 800,
    category: 'food',
    icon: '🍔',
    image: '/images/burger.jpg'
  },
  {
    id: 3,
    name: {
      ar: 'سلطة خضراء',
      en: 'Green Salad',
      fr: 'Salade Verte'
    },
    price: 450,
    category: 'food',
    icon: '🥗',
    image: '/images/salad.jpg'
  },
  {
    id: 4,
    name: {
      ar: 'قهوة عربية',
      en: 'Arabic Coffee',
      fr: 'Café Arabe'
    },
    price: 250,
    category: 'food',
    icon: '☕',
    image: '/images/coffee.jpg'
  },

  // Clothing Category
  {
    id: 5,
    name: {
      ar: 'قميص قطني',
      en: 'Cotton T-Shirt',
      fr: 'T-shirt en Coton'
    },
    price: 1500,
    category: 'clothing',
    icon: '👕',
    image: '/images/tshirt.jpg'
  },
  {
    id: 6,
    name: {
      ar: 'جينز أزرق',
      en: 'Blue Jeans',
      fr: 'Jean Bleu'
    },
    price: 3200,
    category: 'clothing',
    icon: '👖',
    image: '/images/jeans.jpg'
  },
  {
    id: 7,
    name: {
      ar: 'فستان صيفي',
      en: 'Summer Dress',
      fr: 'Robe d\'Été'
    },
    price: 2800,
    category: 'clothing',
    icon: '👗',
    image: '/images/dress.jpg'
  },
  {
    id: 8,
    name: {
      ar: 'حذاء رياضي',
      en: 'Sports Shoes',
      fr: 'Chaussures de Sport'
    },
    price: 4500,
    category: 'clothing',
    icon: '👟',
    image: '/images/shoes.jpg'
  },

  // Home Category
  {
    id: 9,
    name: {
      ar: 'مصباح طاولة',
      en: 'Table Lamp',
      fr: 'Lampe de Table'
    },
    price: 2200,
    category: 'home',
    icon: '💡',
    image: '/images/lamp.jpg'
  },
  {
    id: 10,
    name: {
      ar: 'وسادة مريحة',
      en: 'Comfort Pillow',
      fr: 'Oreiller Confort'
    },
    price: 1800,
    category: 'home',
    icon: '🛏️',
    image: '/images/pillow.jpg'
  },
  {
    id: 11,
    name: {
      ar: 'مزهرية زجاجية',
      en: 'Glass Vase',
      fr: 'Vase en Verre'
    },
    price: 950,
    category: 'home',
    icon: '🏺',
    image: '/images/vase.jpg'
  },
  {
    id: 12,
    name: {
      ar: 'ساعة حائط',
      en: 'Wall Clock',
      fr: 'Horloge Murale'
    },
    price: 1650,
    category: 'home',
    icon: '🕐',
    image: '/images/clock.jpg'
  },

  // Electronics Category
  {
    id: 13,
    name: {
      ar: 'هاتف ذكي',
      en: 'Smartphone',
      fr: 'Smartphone'
    },
    price: 45000,
    category: 'electronics',
    icon: '📱',
    image: '/images/phone.jpg'
  },
  {
    id: 14,
    name: {
      ar: 'سماعات لاسلكية',
      en: 'Wireless Headphones',
      fr: 'Écouteurs Sans Fil'
    },
    price: 8500,
    category: 'electronics',
    icon: '🎧',
    image: '/images/headphones.jpg'
  },
  {
    id: 15,
    name: {
      ar: 'جهاز كمبيوتر محمول',
      en: 'Laptop Computer',
      fr: 'Ordinateur Portable'
    },
    price: 85000,
    category: 'electronics',
    icon: '💻',
    image: '/images/laptop.jpg'
  },
  {
    id: 16,
    name: {
      ar: 'ساعة ذكية',
      en: 'Smart Watch',
      fr: 'Montre Connectée'
    },
    price: 12000,
    category: 'electronics',
    icon: '⌚',
    image: '/images/watch.jpg'
  }
];

export const formatPrice = (price) => {
  return new Intl.NumberFormat('ar-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0
  }).format(price);
};
