# 👨‍💻 دليل المطور - نظام iCalDZ
# Developer Guide - iCalDZ System

## 🏗️ هيكل المشروع / Project Structure

```
icaldz/
├── 📁 src/                     # مجلد الكود المصدري
│   ├── 📄 App.jsx             # المكون الرئيسي
│   ├── 📄 main.jsx            # نقطة دخول React
│   ├── 📄 index.css           # الأنماط الرئيسية
│   ├── 📄 activation.js       # نظام التفعيل
│   ├── 📄 translations.js     # ملفات الترجمة
│   ├── 📄 LanguageContext.jsx # سياق اللغات
│   ├── 📄 SoundManager.js     # إدارة الأصوات
│   ├── 📄 KeyboardShortcuts.js # اختصارات لوحة المفاتيح
│   ├── 📄 dataSync.js         # مزامنة البيانات
│   ├── 📄 profitCalculator.js # حساب الأرباح
│   ├── 📄 newReportSystem.js  # نظام التقارير
│   └── 📁 data/               # بيانات تجريبية
├── 📁 public/                 # الملفات العامة
│   ├── 📁 sounds/             # ملفات الصوت
│   └── 📁 assets/             # الصور والأيقونات
├── 📁 dist/                   # النسخة المبنية
├── 📄 main.js                 # Electron الرئيسي
├── 📄 package.json            # تكوين المشروع
├── 📄 vite.config.js          # تكوين Vite
└── 📄 README.md               # دليل المشروع
```

## 🔧 إعداد بيئة التطوير / Development Environment Setup

### 1️⃣ المتطلبات الأساسية / Prerequisites
```bash
# Node.js (الإصدار 18 أو أحدث)
node --version  # v18.0.0+

# npm (يأتي مع Node.js)
npm --version   # 8.0.0+

# Git (للتحكم في الإصدارات)
git --version   # 2.30.0+
```

### 2️⃣ استنساخ المشروع / Clone Project
```bash
# استنساخ المستودع
git clone https://github.com/icodedz/icaldz-accounting.git
cd icaldz-accounting

# تثبيت المتطلبات
npm install

# تشغيل وضع التطوير
npm run dev
```

### 3️⃣ البرامج المساعدة / Development Tools
```bash
# محرر النصوص المفضل
- Visual Studio Code (مستحسن)
- WebStorm
- Sublime Text

# إضافات VS Code مفيدة
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer
```

## ⚛️ هيكل React / React Architecture

### 🧩 المكونات الرئيسية / Main Components

#### App.jsx - المكون الرئيسي
```javascript
function AppContent() {
  const { t, currentLanguage } = useLanguage();
  
  // حالات التطبيق الرئيسية
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [savedInvoices, setSavedInvoices] = useState([]);
  
  // منطق التطبيق الرئيسي
  return (
    <div className={`app ${currentLanguage}`}>
      {/* محتوى التطبيق */}
    </div>
  );
}
```

#### LanguageContext.jsx - سياق اللغات
```javascript
export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('ar');
  
  const changeLanguage = async (langCode) => {
    setCurrentLanguage(langCode);
    updateDocumentDirection(langCode);
    // حفظ اللغة في localStorage
    localStorage.setItem('icaldz-language', langCode);
  };
  
  const t = (key, fallback) => {
    return getTranslation(key, currentLanguage) || fallback || key;
  };
  
  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
```

### 🎣 Hooks المخصصة / Custom Hooks

#### useLocalStorage Hook
```javascript
export function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}
```

#### useKeyboardShortcuts Hook
```javascript
export function useKeyboardShortcuts(shortcuts, enabled = true) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (event) => {
      const key = getKeyString(event);
      if (shortcuts[key]) {
        event.preventDefault();
        shortcuts[key]();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [shortcuts, enabled]);
}
```

## 🔐 نظام التفعيل / Activation System

### 🏗️ بنية نظام التفعيل / Activation Architecture

#### ActivationCodeGenerator Class
```javascript
export class ActivationCodeGenerator {
  constructor() {
    this.secretKey = 'iCalDZ-2025-Lifetime-Secret-Key-v1.0';
    this.prefix = 'ICAL';
    this.year = new Date().getFullYear();
  }

  generateActivationCode(clientId = null, type = 'LIFETIME', trialDays = null) {
    const data = {
      prefix: this.prefix,
      year: this.year,
      clientId: clientId || this.generateUniqueId(),
      timestamp: Date.now(),
      type: type,
      trialDays: trialDays,
      expiryDate: type === 'TRIAL' ? this.calculateExpiryDate(trialDays) : null
    };

    const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey).toString();
    return this.formatActivationCode(encrypted);
  }

  validateActivationCode(activationCode, machineId = null) {
    try {
      // فك التشفير والتحقق من صحة الكود
      const decrypted = CryptoJS.AES.decrypt(activationCode, this.secretKey);
      const data = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
      
      // التحقق من صحة البيانات
      if (data.type === 'TRIAL' && new Date() > new Date(data.expiryDate)) {
        return { valid: false, error: 'انتهت صلاحية كود التجربة' };
      }
      
      return { valid: true, data: data };
    } catch (error) {
      return { valid: false, error: 'كود تفعيل غير صحيح' };
    }
  }
}
```

#### ActivationManager Class
```javascript
export class ActivationManager {
  constructor() {
    this.generator = new ActivationCodeGenerator();
    this.storageKey = 'icaldz-activation-data';
  }

  checkActivationStatus() {
    const activationData = localStorage.getItem(this.storageKey);
    if (!activationData) {
      return { activated: false, reason: 'لم يتم التفعيل بعد' };
    }

    const data = JSON.parse(activationData);
    const currentMachineId = this.generateMachineFingerprint();
    
    if (data.machineId !== currentMachineId) {
      return { activated: false, reason: 'تم تفعيل البرنامج على جهاز آخر' };
    }

    // التحقق من انتهاء صلاحية التجربة
    if (data.type === 'TRIAL' && new Date() > new Date(data.expiryDate)) {
      return { activated: false, reason: 'انتهت فترة التجربة' };
    }

    return { activated: true, type: data.type, daysLeft: this.calculateDaysLeft(data) };
  }

  activateProgram(activationCode) {
    const machineId = this.generateMachineFingerprint();
    const validation = this.generator.validateActivationCode(activationCode, machineId);
    
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // حفظ بيانات التفعيل
    const activationData = {
      activationCode: activationCode,
      activationDate: new Date().toISOString(),
      machineId: machineId,
      type: validation.data.type,
      expiryDate: validation.data.expiryDate
    };

    localStorage.setItem(this.storageKey, JSON.stringify(activationData));
    return { success: true, data: activationData };
  }
}
```

## 🌍 نظام الترجمة / Translation System

### 📝 هيكل ملفات الترجمة / Translation File Structure

#### translations.js
```javascript
export const translations = {
  ar: {
    // الواجهة الرئيسية
    dashboard: "لوحة التحكم",
    sales: "المبيعات",
    products: "المنتجات",
    customers: "العملاء",
    
    // الرسائل
    saveSuccess: "تم الحفظ بنجاح",
    deleteConfirm: "هل أنت متأكد من الحذف؟",
    
    // الأخطاء
    errorSaving: "خطأ في الحفظ",
    errorLoading: "خطأ في التحميل"
  },
  
  fr: {
    dashboard: "Tableau de bord",
    sales: "Ventes",
    products: "Produits",
    customers: "Clients",
    
    saveSuccess: "Enregistré avec succès",
    deleteConfirm: "Êtes-vous sûr de vouloir supprimer?",
    
    errorSaving: "Erreur lors de l'enregistrement",
    errorLoading: "Erreur lors du chargement"
  },
  
  en: {
    dashboard: "Dashboard",
    sales: "Sales",
    products: "Products",
    customers: "Customers",
    
    saveSuccess: "Saved successfully",
    deleteConfirm: "Are you sure you want to delete?",
    
    errorSaving: "Error saving",
    errorLoading: "Error loading"
  }
};

export function getTranslation(key, language = 'ar') {
  return translations[language]?.[key] || translations.ar[key] || key;
}
```

### 🎨 تكوين التخطيط حسب اللغة / Language Layout Configuration

#### LANGUAGES Configuration
```javascript
export const LANGUAGES = {
  ar: {
    code: 'ar',
    name: 'العربية',
    direction: 'rtl',
    sidebarPosition: 'right',
    floatingButtonsPosition: 'left',
    textAlign: 'right',
    fontFamily: 'Cairo, sans-serif'
  },
  fr: {
    code: 'fr',
    name: 'Français',
    direction: 'ltr',
    sidebarPosition: 'left',
    floatingButtonsPosition: 'right',
    textAlign: 'left',
    fontFamily: 'Inter, sans-serif'
  },
  en: {
    code: 'en',
    name: 'English',
    direction: 'ltr',
    sidebarPosition: 'left',
    floatingButtonsPosition: 'right',
    textAlign: 'left',
    fontFamily: 'Inter, sans-serif'
  }
};
```

## 💾 إدارة البيانات / Data Management

### 🗄️ LocalStorage Manager
```javascript
export class LocalStorageManager {
  static setItem(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      return false;
    }
  }

  static getItem(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  }

  static removeItem(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error removing from localStorage:', error);
      return false;
    }
  }

  static clear() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }
}
```

### 🔄 Data Sync Manager
```javascript
export class DataSyncManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInterval = 5 * 60 * 1000; // 5 دقائق
  }

  async syncAllData() {
    const dataKeys = [
      'icaldz-products',
      'icaldz-customers',
      'icaldz-invoices',
      'icaldz-purchases',
      'icaldz-suppliers',
      'icaldz-expenses'
    ];

    for (const key of dataKeys) {
      await this.syncDataKey(key);
    }
  }

  async syncDataKey(key) {
    try {
      const localData = LocalStorageManager.getItem(key, []);
      // منطق المزامنة مع الخادم
      console.log(`Syncing ${key}:`, localData.length, 'items');
    } catch (error) {
      console.error(`Error syncing ${key}:`, error);
    }
  }

  exportToJSON() {
    const data = {};
    const keys = Object.keys(localStorage).filter(key => key.startsWith('icaldz-'));
    
    keys.forEach(key => {
      data[key] = LocalStorageManager.getItem(key);
    });

    return JSON.stringify(data, null, 2);
  }

  importFromJSON(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      Object.entries(data).forEach(([key, value]) => {
        LocalStorageManager.setItem(key, value);
      });
      return true;
    } catch (error) {
      console.error('Error importing JSON:', error);
      return false;
    }
  }
}
```

## 🎵 نظام الصوت / Sound System

### 🔊 Sound Manager
```javascript
export class SoundManager {
  static sounds = {
    success: '/sounds/success.mp3',
    error: '/sounds/error.mp3',
    warning: '/sounds/warning.mp3',
    newInvoice: '/sounds/new-invoice.mp3',
    saveInvoice: '/sounds/save-invoice.mp3',
    print: '/sounds/print.mp3',
    addProduct: '/sounds/add-product.mp3',
    delete: '/sounds/delete.mp3'
  };

  static isEnabled = true;
  static volume = 0.5;

  static async init() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.isEnabled = LocalStorageManager.getItem('soundEnabled', true);
      this.volume = LocalStorageManager.getItem('soundVolume', 0.5);
      console.log('🎵 Sound system initialized');
    } catch (error) {
      console.error('🔇 Failed to initialize sound system:', error);
    }
  }

  static async play(soundName, options = {}) {
    if (!this.isEnabled) return;

    try {
      const soundPath = this.sounds[soundName];
      if (!soundPath) {
        console.warn(`Sound "${soundName}" not found`);
        return;
      }

      const audio = new Audio(soundPath);
      audio.volume = options.volume || this.volume;
      await audio.play();

      if (options.showNotification !== false) {
        console.log(`🔊 Played sound: ${soundName}`);
      }
    } catch (error) {
      console.warn('Could not play sound:', error);
    }
  }

  static setEnabled(enabled) {
    this.isEnabled = enabled;
    LocalStorageManager.setItem('soundEnabled', enabled);
  }

  static setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    LocalStorageManager.setItem('soundVolume', this.volume);
  }
}
```

## ⌨️ نظام الاختصارات / Keyboard Shortcuts System

### 🎯 Keyboard Shortcuts Manager
```javascript
export class KeyboardShortcuts {
  constructor(callbacks = {}) {
    this.callbacks = callbacks;
    this.isEnabled = true;
    this.shortcuts = {
      'F1': () => this.callbacks.openSalesInvoice?.(),
      'F3': () => this.callbacks.addProduct?.(),
      'F6': () => this.callbacks.newProduct?.(),
      'F7': () => this.callbacks.openPurchaseInvoice?.(),
      'F5': () => this.callbacks.refreshData?.(),
      'Enter': () => this.callbacks.saveInvoice?.(),
      'Shift+Enter': () => this.callbacks.saveAndPrint?.(),
      'Escape': () => this.callbacks.closeModal?.(),
      'Ctrl+D': () => this.callbacks.goToDashboard?.(),
      'Ctrl+S': () => this.callbacks.goToSales?.(),
      'Ctrl+P': () => this.callbacks.goToProducts?.(),
      'Ctrl+C': () => this.callbacks.goToCustomers?.(),
      'Ctrl+R': () => this.callbacks.goToReports?.()
    };
  }

  init() {
    document.addEventListener('keydown', this.handleKeyPress.bind(this));
    console.log('⌨️ Keyboard shortcuts initialized');
  }

  handleKeyPress(event) {
    if (!this.isEnabled) return;

    const key = this.getKeyString(event);
    const shortcut = this.shortcuts[key];

    if (shortcut) {
      event.preventDefault();
      shortcut();
      SoundManager.play('success', { showNotification: false });
    }
  }

  getKeyString(event) {
    const parts = [];
    if (event.ctrlKey) parts.push('Ctrl');
    if (event.shiftKey) parts.push('Shift');
    if (event.altKey) parts.push('Alt');
    
    const key = event.key === ' ' ? 'Space' : event.key;
    parts.push(key);
    
    return parts.join('+');
  }

  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  updateCallbacks(newCallbacks) {
    this.callbacks = { ...this.callbacks, ...newCallbacks };
  }
}
```

## 📊 نظام التقارير / Reporting System

### 📈 Report Generator
```javascript
export function generateCleanDailyReport(invoices, products, expenses, date) {
  const report = {
    date: date.toISOString().split('T')[0],
    totalSales: 0,
    costOfGoodsSold: 0,
    grossProfit: 0,
    totalExpenses: 0,
    netProfit: 0,
    invoiceCount: 0,
    processedItems: 0,
    warnings: [],
    hasErrors: false
  };

  // فلترة فواتير اليوم
  const todayInvoices = invoices.filter(inv => inv.date === report.date);
  
  // حساب إجمالي المبيعات
  report.totalSales = todayInvoices.reduce((sum, inv) => {
    return sum + (parseFloat(inv.finalTotal) || 0);
  }, 0);
  
  report.invoiceCount = todayInvoices.length;

  // حساب تكلفة البضاعة المباعة
  const costCalculation = calculateCostOfGoodsSold(todayInvoices, products);
  report.costOfGoodsSold = costCalculation.totalCost;
  report.processedItems = costCalculation.processedItems;

  // حساب الربح الإجمالي
  report.grossProfit = report.totalSales - report.costOfGoodsSold;

  // حساب المصاريف
  const todayExpenses = expenses.filter(exp => exp.date === report.date);
  report.totalExpenses = todayExpenses.reduce((sum, exp) => {
    return sum + (parseFloat(exp.amount) || 0);
  }, 0);

  // حساب صافي الربح
  report.netProfit = report.grossProfit - report.totalExpenses;

  return report;
}

export function calculateCostOfGoodsSold(invoices, products) {
  let totalCost = 0;
  let processedItems = 0;
  const missingProducts = [];

  invoices.forEach(invoice => {
    invoice.items.forEach(item => {
      const product = products.find(p => p.id === item.productId);
      
      if (product) {
        const itemCost = (parseFloat(product.buyPrice) || 0) * (parseFloat(item.quantity) || 0);
        totalCost += itemCost;
        processedItems++;
      } else {
        missingProducts.push(item.productId);
      }
    });
  });

  return {
    totalCost,
    processedItems,
    missingProducts: [...new Set(missingProducts)]
  };
}
```

## 🔧 أدوات التطوير / Development Tools

### 🛠️ Build Scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "electron": "electron .",
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"",
    "dist": "npm run build && electron-builder --publish=never",
    "dist:win": "npm run build && electron-builder --win",
    "dist:mac": "npm run build && electron-builder --mac",
    "dist:linux": "npm run build && electron-builder --linux",
    "pack": "npm run build && electron-builder --dir",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"
  }
}
```

### 🧪 Testing Setup
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  transform: {
    '^.+\\.(js|jsx)$': 'babel-jest'
  }
};

// Example test
import { render, screen } from '@testing-library/react';
import { LanguageProvider } from './LanguageContext';
import App from './App';

test('renders dashboard', () => {
  render(
    <LanguageProvider>
      <App />
    </LanguageProvider>
  );
  
  const dashboardElement = screen.getByText(/لوحة التحكم/i);
  expect(dashboardElement).toBeInTheDocument();
});
```

---

**© 2025 iCode DZ - جميع الحقوق محفوظة / All Rights Reserved**
