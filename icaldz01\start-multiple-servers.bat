@echo off
title نظام المحاسبي - خوادم متعددة مع مزامنة البيانات
color 0B

echo.
echo ========================================
echo    نظام المحاسبي - خوادم متعددة
echo.
echo         تطوير: iCode DZ
echo         الهاتف: +213 551 93 05 89
echo         البريد: <EMAIL>
echo ========================================
echo.

echo [1] تشغيل خادم واحد (3000)
echo [2] تشغيل خادمين (3000 + 3001)
echo [3] تشغيل ثلاثة خوادم (3000 + 3001 + 3002)
echo [4] تشغيل خادم المزامنة فقط
echo [5] إيقاف جميع الخوادم
echo [6] تثبيت التبعيات المطلوبة
echo [0] خروج
echo.

set /p choice="اختر رقم العملية: "

if "%choice%"=="1" (
    echo.
    echo جاري تشغيل خادم واحد على المنفذ 3000...
    echo سيتم فتح المتصفح تلقائياً على http://localhost:3000
    echo.
    start http://localhost:3000
    npm run dev
) else if "%choice%"=="2" (
    echo.
    echo جاري تشغيل خادمين...
    echo الخادم الأول: http://localhost:3000
    echo الخادم الثاني: http://localhost:3001
    echo.
    start "خادم 3000" cmd /k "npm run dev"
    timeout /t 3 /nobreak >nul
    start "خادم 3001" cmd /k "npm run dev:3001"
    timeout /t 2 /nobreak >nul
    start http://localhost:3000
    start http://localhost:3001
    echo تم تشغيل الخادمين بنجاح!
    pause
) else if "%choice%"=="3" (
    echo.
    echo جاري تشغيل ثلاثة خوادم مع مزامنة البيانات...
    echo الخادم الأول: http://localhost:3000
    echo الخادم الثاني: http://localhost:3001
    echo الخادم الثالث: http://localhost:3002
    echo.
    
    REM Build the project first
    echo جاري بناء المشروع...
    call npm run build
    
    REM Start sync servers
    start "خادم مزامنة 3000" cmd /k "npm run sync-server"
    timeout /t 3 /nobreak >nul
    start "خادم مزامنة 3001" cmd /k "npm run sync-server:3001"
    timeout /t 3 /nobreak >nul
    start "خادم مزامنة 3002" cmd /k "npm run sync-server:3002"
    timeout /t 3 /nobreak >nul
    
    REM Open browsers
    start http://localhost:3000
    timeout /t 2 /nobreak >nul
    start http://localhost:3001
    timeout /t 2 /nobreak >nul
    start http://localhost:3002
    
    echo.
    echo ✅ تم تشغيل جميع الخوادم بنجاح!
    echo 🔄 نظام المزامنة نشط - البيانات ستتزامن تلقائياً
    echo.
    echo للاختبار:
    echo 1. افتح http://localhost:3000 وسجل دخول
    echo 2. أضف بائع جديد أو منتج
    echo 3. افتح http://localhost:3001 وتحقق من المزامنة
    echo 4. كرر مع http://localhost:3002
    echo.
    pause
) else if "%choice%"=="4" (
    echo.
    echo جاري تشغيل خادم المزامنة فقط...
    npm run sync-server
) else if "%choice%"=="5" (
    echo.
    echo جاري إيقاف جميع الخوادم...
    taskkill /f /im node.exe 2>nul
    taskkill /f /im cmd.exe /fi "WINDOWTITLE eq خادم*" 2>nul
    echo تم إيقاف جميع الخوادم
    pause
) else if "%choice%"=="6" (
    echo.
    echo جاري تثبيت التبعيات المطلوبة...
    npm install express cors
    echo.
    echo تم تثبيت التبعيات بنجاح!
    pause
) else if "%choice%"=="0" (
    exit
) else (
    echo.
    echo خيار غير صحيح!
    pause
    goto start
)

:start
cls
goto :eof
