const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * iCalDZ Activation Code Generator - English Version
 * Compatible Lifetime License System
 */
class ActivationCodeGenerator {
  constructor() {
    this.secretKey = 'iCalDZ-2025-Lifetime-Secret-Key-v1.0-EN';
    this.prefix = 'ICAL';
    this.year = new Date().getFullYear();
    this.usedCodesFile = path.join(__dirname, 'used-codes-en.json');
  }

  /**
   * Generate activation code
   */
  generateActivationCode(clientName = null, machineId = null) {
    try {
      const uniqueId = this.generateUniqueId();

      const data = {
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        clientName: clientName || 'General Client',
        timestamp: Date.now(),
        type: 'LIFETIME',
        machineId: machineId || null,
        securityLevel: machineId ? 'MACHINE_SPECIFIC' : 'UNIVERSAL'
      };

      // Create a simple hash-based code (compatible approach)
      const dataString = JSON.stringify(data);
      const hash = crypto.createHash('sha256').update(dataString + this.secretKey).digest('hex');
      
      const code = this.formatActivationCode(hash);
      
      // Store code data for validation
      this.storeCodeData(code, data);

      return {
        activationCode: code,
        clientId: uniqueId,
        clientName: data.clientName,
        machineId: machineId,
        generatedAt: new Date().toISOString(),
        type: 'LIFETIME',
        securityLevel: data.securityLevel
      };
    } catch (error) {
      console.error('Error generating activation code:', error);
      return null;
    }
  }

  /**
   * Format activation code for readability
   */
  formatActivationCode(hash) {
    const cleanCode = hash.replace(/[^A-Za-z0-9]/g, '').substring(0, 32).toUpperCase();
    const groups = cleanCode.match(/.{1,4}/g) || [];
    return `${this.prefix}-${this.year}-${groups.join('-')}`;
  }

  /**
   * Generate unique identifier
   */
  generateUniqueId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}${random}`.toUpperCase();
  }

  /**
   * Store code data for validation
   */
  storeCodeData(activationCode, data) {
    try {
      let codesData = {};
      
      if (fs.existsSync(this.usedCodesFile)) {
        codesData = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      }
      
      codesData[activationCode] = {
        data: data,
        createdAt: new Date().toISOString(),
        used: false
      };
      
      fs.writeFileSync(this.usedCodesFile, JSON.stringify(codesData, null, 2));
    } catch (error) {
      console.error('Error storing code data:', error);
    }
  }

  /**
   * Validate activation code
   */
  validateActivationCode(activationCode, machineId = null) {
    try {
      // Check format
      if (!activationCode.startsWith(`${this.prefix}-${this.year}-`)) {
        return { valid: false, error: 'Invalid code format' };
      }

      // Check if code exists in our database
      if (!fs.existsSync(this.usedCodesFile)) {
        return { valid: false, error: 'Code database not found' };
      }

      const codesData = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      
      if (!codesData[activationCode]) {
        return { valid: false, error: 'Code not found or invalid' };
      }

      const codeInfo = codesData[activationCode];
      const data = codeInfo.data;
      
      // Check if machine-specific code matches machine
      if (data.machineId && machineId && data.machineId !== machineId) {
        return { 
          valid: false, 
          error: 'This code is for a different machine' 
        };
      }

      return {
        valid: true,
        data: data,
        used: codeInfo.used,
        message: 'Valid activation code'
      };
    } catch (error) {
      console.error('Validation error:', error);
      return { valid: false, error: 'Validation error' };
    }
  }

  /**
   * Display formatted result
   */
  displayResult(result) {
    if (!result) {
      console.log('❌ ERROR: Failed to generate activation code');
      return;
    }

    console.log('✅ SUCCESS: Activation code generated successfully!');
    console.log('');
    console.log('========================================================================');
    console.log('                        ACTIVATION CODE DETAILS');
    console.log('========================================================================');
    console.log('');
    console.log(`📋 Activation Code: ${result.activationCode}`);
    console.log(`👤 Client Name: ${result.clientName}`);
    console.log(`🆔 Client ID: ${result.clientId}`);
    console.log(`📅 Generated: ${new Date(result.generatedAt).toLocaleString()}`);
    console.log(`🔒 Type: ${result.type}`);
    console.log(`🛡️  Security Level: ${result.securityLevel}`);
    
    if (result.machineId) {
      console.log(`💻 Machine ID: ${result.machineId}`);
    }
    
    console.log('');
    console.log('========================================================================');
    console.log('                            IMPORTANT NOTES');
    console.log('========================================================================');
    console.log('');
    console.log('⚠️  Each activation code can only be used ONCE');
    console.log('🔐 Keep this code secure and confidential');
    console.log('💾 Save this information in a safe place');
    console.log('📞 Support: +213 551 93 05 89');
    console.log('📧 Email: <EMAIL>');
    console.log('');
    console.log('========================================================================');
  }
}

// Main execution
function main() {
  const generator = new ActivationCodeGenerator();
  const args = process.argv.slice(2);

  // Handle validation
  if (args[0] === '--validate') {
    const codeToValidate = args[1];
    const machineId = args[2];
    
    if (!codeToValidate) {
      console.log('❌ ERROR: Please provide an activation code to validate');
      return;
    }

    console.log('🔍 Validating activation code...');
    console.log('');
    
    const validation = generator.validateActivationCode(codeToValidate, machineId);
    
    if (validation.valid) {
      console.log('✅ SUCCESS: Valid activation code!');
      console.log('');
      console.log('========================================================================');
      console.log('                        VALIDATION RESULTS');
      console.log('========================================================================');
      console.log('');
      console.log(`📋 Code: ${codeToValidate}`);
      console.log(`👤 Client: ${validation.data.clientName}`);
      console.log(`🆔 Client ID: ${validation.data.clientId}`);
      console.log(`🔒 Type: ${validation.data.type}`);
      console.log(`🛡️  Security: ${validation.data.securityLevel}`);
      
      if (validation.data.machineId) {
        console.log(`💻 Machine ID: ${validation.data.machineId}`);
      }
      
      console.log(`📊 Status: ${validation.used ? '🔴 USED' : '🟢 AVAILABLE'}`);
      
      console.log('');
      console.log('========================================================================');
    } else {
      console.log('❌ INVALID: Activation code validation failed');
      console.log('');
      console.log(`🚫 Error: ${validation.error}`);
      console.log('');
      console.log('========================================================================');
    }
    return;
  }

  // Generate new activation code
  const clientName = args[0] || null;
  const machineId = args[1] || null;
  
  console.log('🔄 Generating new activation code...');
  console.log('');
  
  const result = generator.generateActivationCode(clientName, machineId);
  generator.displayResult(result);
}

// Run the program
if (require.main === module) {
  main();
}

module.exports = ActivationCodeGenerator;
