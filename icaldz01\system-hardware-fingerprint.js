/**
 * System-Level Hardware Fingerprinting
 * True hardware identification that works across browsers
 * 
 * <AUTHOR> DZ
 * @version 3.0.0
 */

const os = require('os');
const crypto = require('crypto');
const { execSync } = require('child_process');

class SystemHardwareFingerprint {
  constructor() {
    this.platform = os.platform();
  }

  /**
   * Generate true hardware fingerprint using system-level information
   */
  generateSystemFingerprint() {
    try {
      const hardwareInfo = {
        // CPU Information
        cpuInfo: this.getCPUInfo(),
        
        // Memory Information
        memoryInfo: this.getMemoryInfo(),
        
        // Motherboard Information
        motherboardInfo: this.getMotherboardInfo(),
        
        // Storage Information
        storageInfo: this.getStorageInfo(),
        
        // Network Hardware
        networkInfo: this.getNetworkHardware(),
        
        // System Information
        systemInfo: this.getSystemInfo(),
        
        // BIOS Information
        biosInfo: this.getBIOSInfo()
      };

      // Create a stable hash from hardware information
      const fingerprintData = JSON.stringify(hardwareInfo, Object.keys(hardwareInfo).sort());
      const fingerprint = crypto.createHash('sha256').update(fingerprintData).digest('hex');
      
      return {
        fingerprint: fingerprint.substring(0, 32).toUpperCase(),
        components: hardwareInfo,
        platform: this.platform,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('System fingerprinting error:', error);
      return this.getFallbackFingerprint();
    }
  }

  /**
   * Get CPU information
   */
  getCPUInfo() {
    try {
      const cpus = os.cpus();
      if (cpus && cpus.length > 0) {
        return {
          model: cpus[0].model,
          cores: cpus.length,
          speed: cpus[0].speed,
          architecture: os.arch()
        };
      }
      return { model: 'unknown', cores: 0, speed: 0, architecture: os.arch() };
    } catch (error) {
      return { error: 'cpu_info_failed' };
    }
  }

  /**
   * Get memory information
   */
  getMemoryInfo() {
    try {
      return {
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        memoryRatio: Math.round((os.totalmem() / (1024 * 1024 * 1024)) * 100) / 100 // GB
      };
    } catch (error) {
      return { error: 'memory_info_failed' };
    }
  }

  /**
   * Get motherboard/system information
   */
  getMotherboardInfo() {
    try {
      if (this.platform === 'win32') {
        // Windows: Get motherboard info using wmic
        const motherboard = execSync('wmic baseboard get serialnumber,manufacturer,product /format:csv', { encoding: 'utf8', timeout: 5000 });
        const bios = execSync('wmic bios get serialnumber /format:csv', { encoding: 'utf8', timeout: 5000 });
        
        return {
          motherboard: this.parseWMICOutput(motherboard),
          bios: this.parseWMICOutput(bios),
          platform: 'windows'
        };
      } else if (this.platform === 'linux') {
        // Linux: Get hardware info from /proc and dmidecode
        const machineId = execSync('cat /etc/machine-id 2>/dev/null || cat /var/lib/dbus/machine-id 2>/dev/null || echo "no-machine-id"', { encoding: 'utf8', timeout: 5000 }).trim();
        
        return {
          machineId,
          platform: 'linux'
        };
      } else if (this.platform === 'darwin') {
        // macOS: Get hardware UUID
        const hardwareUUID = execSync('system_profiler SPHardwareDataType | grep "Hardware UUID" | awk \'{print $3}\'', { encoding: 'utf8', timeout: 5000 }).trim();
        
        return {
          hardwareUUID,
          platform: 'macos'
        };
      }
      
      return { platform: this.platform, method: 'basic' };
    } catch (error) {
      return { error: 'motherboard_info_failed', platform: this.platform };
    }
  }

  /**
   * Get storage information
   */
  getStorageInfo() {
    try {
      if (this.platform === 'win32') {
        // Windows: Get disk serial numbers
        const diskInfo = execSync('wmic diskdrive get serialnumber,model,size /format:csv', { encoding: 'utf8', timeout: 5000 });
        return {
          disks: this.parseWMICOutput(diskInfo),
          platform: 'windows'
        };
      } else if (this.platform === 'linux') {
        // Linux: Get disk information
        const diskInfo = execSync('lsblk -d -o NAME,SERIAL,SIZE 2>/dev/null || echo "no-disk-info"', { encoding: 'utf8', timeout: 5000 });
        return {
          disks: diskInfo.trim(),
          platform: 'linux'
        };
      }
      
      return { platform: this.platform, method: 'basic' };
    } catch (error) {
      return { error: 'storage_info_failed' };
    }
  }

  /**
   * Get network hardware information
   */
  getNetworkHardware() {
    try {
      const networkInterfaces = os.networkInterfaces();
      const macAddresses = [];
      
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        for (const iface of interfaces) {
          if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
            macAddresses.push({
              interface: interfaceName,
              mac: iface.mac,
              family: iface.family
            });
          }
        }
      }
      
      return {
        macAddresses,
        hostname: os.hostname()
      };
    } catch (error) {
      return { error: 'network_info_failed' };
    }
  }

  /**
   * Get system information
   */
  getSystemInfo() {
    try {
      return {
        platform: os.platform(),
        release: os.release(),
        version: os.version ? os.version() : 'unknown',
        arch: os.arch(),
        hostname: os.hostname(),
        uptime: os.uptime()
      };
    } catch (error) {
      return { error: 'system_info_failed' };
    }
  }

  /**
   * Get BIOS information
   */
  getBIOSInfo() {
    try {
      if (this.platform === 'win32') {
        const biosInfo = execSync('wmic bios get serialnumber,manufacturer,version /format:csv', { encoding: 'utf8', timeout: 5000 });
        return this.parseWMICOutput(biosInfo);
      }
      return { platform: this.platform, method: 'not_available' };
    } catch (error) {
      return { error: 'bios_info_failed' };
    }
  }

  /**
   * Parse WMIC output (Windows)
   */
  parseWMICOutput(output) {
    try {
      const lines = output.split('\n').filter(line => line.trim() && !line.includes('Node,'));
      const result = {};
      
      lines.forEach((line, index) => {
        const parts = line.split(',');
        if (parts.length > 1) {
          result[`entry_${index}`] = parts.join('|');
        }
      });
      
      return result;
    } catch (error) {
      return { raw: output.substring(0, 100) };
    }
  }

  /**
   * Fallback fingerprint if system methods fail
   */
  getFallbackFingerprint() {
    const fallbackData = {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      cpuCount: os.cpus().length,
      totalMemory: os.totalmem(),
      release: os.release(),
      timestamp: Date.now()
    };
    
    const fingerprintData = JSON.stringify(fallbackData);
    const fingerprint = crypto.createHash('sha256').update(fingerprintData).digest('hex');
    
    return {
      fingerprint: fingerprint.substring(0, 32).toUpperCase(),
      components: fallbackData,
      platform: this.platform,
      method: 'fallback',
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Validate hardware fingerprint
   */
  validateFingerprint(storedFingerprint, tolerance = 0.8) {
    try {
      const currentFingerprint = this.generateSystemFingerprint();
      
      if (currentFingerprint.fingerprint === storedFingerprint) {
        return { valid: true, match: 'exact', confidence: 1.0 };
      }
      
      // Check component-level similarity for partial matches
      // This allows for minor hardware changes while maintaining security
      const similarity = this.calculateSimilarity(currentFingerprint, storedFingerprint);
      
      if (similarity >= tolerance) {
        return { 
          valid: true, 
          match: 'partial', 
          confidence: similarity,
          warning: 'Minor hardware changes detected'
        };
      }
      
      return { 
        valid: false, 
        match: 'none', 
        confidence: similarity,
        error: 'Hardware fingerprint mismatch'
      };
    } catch (error) {
      return { 
        valid: false, 
        error: 'Fingerprint validation failed',
        details: error.message 
      };
    }
  }

  /**
   * Calculate similarity between fingerprints
   */
  calculateSimilarity(current, stored) {
    // This is a simplified similarity calculation
    // In production, you'd implement more sophisticated comparison
    try {
      if (typeof stored === 'string') {
        return current.fingerprint === stored ? 1.0 : 0.0;
      }
      
      // Compare key components
      const keyComponents = ['cpuInfo', 'memoryInfo', 'networkInfo', 'systemInfo'];
      let matches = 0;
      let total = keyComponents.length;
      
      keyComponents.forEach(component => {
        if (current.components[component] && stored.components && stored.components[component]) {
          const currentStr = JSON.stringify(current.components[component]);
          const storedStr = JSON.stringify(stored.components[component]);
          if (currentStr === storedStr) {
            matches++;
          }
        }
      });
      
      return matches / total;
    } catch (error) {
      return 0.0;
    }
  }
}

module.exports = SystemHardwareFingerprint;
