/**
 * Enhanced Security System for iCalDZ
 * Multiple layers of protection beyond machine ID
 *
 * <AUTHOR> DZ
 * @version 2.0.0
 */

import CryptoJS from 'crypto-js';

/**
 * Advanced Security Manager with Multiple Protection Layers
 */
export class EnhancedSecurityManager {
  constructor() {
    this.secretKey = 'iCalDZ-2025-Enhanced-Security-Key-v2.0';
    this.serverUrl = 'https://api.icodedz.com/activation'; // Server validation endpoint
    this.maxActivationAttempts = 3;
    this.lockoutDuration = 24 * 60 * 60 * 1000; // 24 hours
    this.heartbeatInterval = 5 * 60 * 1000; // 5 minutes
    this.securityLevels = {
      BASIC: 1,
      ENHANCED: 2,
      MAXIMUM: 3
    };
  }

  /**
   * 1. Time-Based One-Time Password (TOTP) Generation
   */
  generateTOTP(secret, timeWindow = 30) {
    try {
      const epoch = Math.floor(Date.now() / 1000);
      const timeStep = Math.floor(epoch / timeWindow);
      const hmac = CryptoJS.HmacSHA1(timeStep.toString(), secret);
      const offset = parseInt(hmac.toString().slice(-1), 16);
      const code = parseInt(hmac.toString().substr(offset * 2, 8), 16) % 1000000;
      return code.toString().padStart(6, '0');
    } catch (error) {
      console.error('TOTP generation error:', error);
      return null;
    }
  }

  /**
   * 2. Hardware-Based Fingerprinting (Enhanced)
   */
  async generateHardwareFingerprint() {
    try {
      const fingerprint = {
        // CPU Information
        cpuCores: navigator.hardwareConcurrency || 0,
        deviceMemory: navigator.deviceMemory || 0,

        // GPU Information
        webglInfo: await this.getWebGLInfo(),

        // Audio Context Fingerprinting
        audioFingerprint: await this.generateAudioFingerprint(),

        // Battery API (if available)
        batteryInfo: await this.getBatteryInfo(),

        // Network Information
        networkInfo: this.getNetworkInfo(),

        // Media Devices
        mediaDevices: await this.getMediaDevicesInfo(),

        // Timezone and Locale
        timezoneInfo: this.getTimezoneInfo(),

        // Performance Timing
        performanceTiming: this.getPerformanceTiming()
      };

      const fingerprintString = JSON.stringify(fingerprint, Object.keys(fingerprint).sort());
      return CryptoJS.SHA256(fingerprintString).toString().substring(0, 32);
    } catch (error) {
      console.error('Hardware fingerprinting error:', error);
      return this.generateFallbackFingerprint();
    }
  }

  /**
   * 3. Behavioral Analysis
   */
  initializeBehavioralTracking() {
    const behaviorData = {
      keystrokes: [],
      mouseMovements: [],
      clickPatterns: [],
      typingSpeed: [],
      sessionStartTime: Date.now()
    };

    // Track keystroke patterns
    document.addEventListener('keydown', (e) => {
      behaviorData.keystrokes.push({
        key: e.code,
        timestamp: Date.now(),
        duration: 0
      });
    });

    document.addEventListener('keyup', (e) => {
      const lastKeystroke = behaviorData.keystrokes[behaviorData.keystrokes.length - 1];
      if (lastKeystroke && lastKeystroke.key === e.code) {
        lastKeystroke.duration = Date.now() - lastKeystroke.timestamp;
      }
    });

    // Track mouse movements
    document.addEventListener('mousemove', (e) => {
      behaviorData.mouseMovements.push({
        x: e.clientX,
        y: e.clientY,
        timestamp: Date.now()
      });

      // Keep only last 100 movements
      if (behaviorData.mouseMovements.length > 100) {
        behaviorData.mouseMovements.shift();
      }
    });

    // Store behavior data
    localStorage.setItem('icaldz-behavior-data', JSON.stringify(behaviorData));
    return behaviorData;
  }

  /**
   * 4. Network-Based Validation
   */
  async validateWithServer(activationCode, machineId, hardwareFingerprint) {
    try {
      const payload = {
        activationCode,
        machineId,
        hardwareFingerprint,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        referrer: document.referrer || 'direct'
      };

      const response = await fetch(`${this.serverUrl}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Security-Token': this.generateSecurityToken(payload)
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Server validation failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Server validation unavailable, using offline validation:', error);
      return { valid: true, offline: true };
    }
  }

  /**
   * 5. Anti-Debugging and Tampering Detection
   */
  initializeAntiTampering() {
    // Detect DevTools
    let devtools = { open: false, orientation: null };

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 ||
          window.outerWidth - window.innerWidth > 200) {
        devtools.open = true;
        this.handleTamperingDetected('DevTools detected');
      }
    }, 1000);

    // Detect debugging
    let start = Date.now();
    debugger;
    if (Date.now() - start > 100) {
      this.handleTamperingDetected('Debugger detected');
    }

    // Detect code modification
    this.verifyCodeIntegrity();

    return devtools;
  }

  /**
   * 6. Geolocation-Based Validation
   */
  async getLocationFingerprint() {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve('NO_GEOLOCATION');
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData = {
            latitude: Math.round(position.coords.latitude * 100) / 100, // Rounded for privacy
            longitude: Math.round(position.coords.longitude * 100) / 100,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          };
          resolve(CryptoJS.SHA256(JSON.stringify(locationData)).toString().substring(0, 16));
        },
        () => resolve('LOCATION_DENIED'),
        { timeout: 5000, maximumAge: 300000 }
      );
    });
  }

  /**
   * 7. Session Heartbeat and Continuous Validation
   */
  startSecurityHeartbeat() {
    const heartbeatInterval = setInterval(async () => {
      try {
        const currentFingerprint = await this.generateHardwareFingerprint();
        const storedFingerprint = localStorage.getItem('icaldz-hardware-fingerprint');

        if (storedFingerprint && currentFingerprint !== storedFingerprint) {
          this.handleSecurityViolation('Hardware fingerprint mismatch');
          clearInterval(heartbeatInterval);
          return;
        }

        // Update last heartbeat
        localStorage.setItem('icaldz-last-heartbeat', Date.now().toString());
        localStorage.setItem('icaldz-hardware-fingerprint', currentFingerprint);

      } catch (error) {
        console.error('Security heartbeat error:', error);
      }
    }, this.heartbeatInterval);

    return heartbeatInterval;
  }

  /**
   * Helper Methods
   */
  async getWebGLInfo() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

      if (!gl) return 'NO_WEBGL';

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      return {
        vendor: gl.getParameter(debugInfo?.UNMASKED_VENDOR_WEBGL || gl.VENDOR),
        renderer: gl.getParameter(debugInfo?.UNMASKED_RENDERER_WEBGL || gl.RENDERER),
        version: gl.getParameter(gl.VERSION),
        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
      };
    } catch (error) {
      return 'WEBGL_ERROR';
    }
  }

  async generateAudioFingerprint() {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      const gainNode = audioContext.createGain();

      oscillator.connect(analyser);
      analyser.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 1000;
      gainNode.gain.value = 0;

      oscillator.start();

      const frequencyData = new Uint8Array(analyser.frequencyBinCount);
      analyser.getByteFrequencyData(frequencyData);

      oscillator.stop();
      audioContext.close();

      return CryptoJS.SHA256(Array.from(frequencyData).join('')).toString().substring(0, 16);
    } catch (error) {
      return 'NO_AUDIO';
    }
  }

  generateSecurityToken(payload) {
    const timestamp = Date.now();
    const nonce = Math.random().toString(36).substring(2);
    const data = JSON.stringify(payload) + timestamp + nonce;
    return CryptoJS.HmacSHA256(data, this.secretKey).toString();
  }

  handleTamperingDetected(reason) {
    console.warn('Tampering detected:', reason);
    localStorage.setItem('icaldz-security-violation', JSON.stringify({
      reason,
      timestamp: Date.now(),
      severity: 'HIGH'
    }));

    // Optionally disable the application
    // this.disableApplication();
  }

  handleSecurityViolation(reason) {
    console.error('Security violation:', reason);
    localStorage.removeItem('icaldz-activation-data');

    // Notify user
    if (window.showSecurityAlert) {
      window.showSecurityAlert('تم اكتشاف انتهاك أمني. يرجى إعادة تفعيل البرنامج.');
    }
  }

  async getBatteryInfo() {
    try {
      if ('getBattery' in navigator) {
        const battery = await navigator.getBattery();
        return {
          charging: battery.charging,
          level: Math.round(battery.level * 100),
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime
        };
      }
      return 'NO_BATTERY_API';
    } catch (error) {
      return 'BATTERY_ERROR';
    }
  }

  getNetworkInfo() {
    try {
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      if (connection) {
        return {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        };
      }
      return 'NO_NETWORK_API';
    } catch (error) {
      return 'NETWORK_ERROR';
    }
  }

  async getMediaDevicesInfo() {
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices();
        return {
          audioInputs: devices.filter(d => d.kind === 'audioinput').length,
          audioOutputs: devices.filter(d => d.kind === 'audiooutput').length,
          videoInputs: devices.filter(d => d.kind === 'videoinput').length
        };
      }
      return 'NO_MEDIA_DEVICES';
    } catch (error) {
      return 'MEDIA_DEVICES_ERROR';
    }
  }

  getTimezoneInfo() {
    try {
      return {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        offset: new Date().getTimezoneOffset(),
        locale: navigator.language,
        languages: navigator.languages ? navigator.languages.slice(0, 3) : []
      };
    } catch (error) {
      return 'TIMEZONE_ERROR';
    }
  }

  getPerformanceTiming() {
    try {
      if (performance && performance.timing) {
        const timing = performance.timing;
        return {
          navigationStart: timing.navigationStart,
          loadEventEnd: timing.loadEventEnd,
          domContentLoadedEventEnd: timing.domContentLoadedEventEnd
        };
      }
      return 'NO_PERFORMANCE_API';
    } catch (error) {
      return 'PERFORMANCE_ERROR';
    }
  }

  verifyCodeIntegrity() {
    try {
      // Check if critical functions have been modified
      const criticalFunctions = [
        'generateHardwareFingerprint',
        'validateWithServer',
        'handleSecurityViolation'
      ];

      criticalFunctions.forEach(funcName => {
        if (typeof this[funcName] !== 'function') {
          this.handleTamperingDetected(`Critical function ${funcName} modified`);
        }
      });

      return true;
    } catch (error) {
      this.handleTamperingDetected('Code integrity check failed');
      return false;
    }
  }

  generateFallbackFingerprint() {
    const fallbackData = [
      navigator.userAgent,
      navigator.platform,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      navigator.language,
      Date.now()
    ].join('-');

    return CryptoJS.SHA256(fallbackData).toString().substring(0, 32);
  }
}

// Export singleton instance
export const enhancedSecurity = new EnhancedSecurityManager();
