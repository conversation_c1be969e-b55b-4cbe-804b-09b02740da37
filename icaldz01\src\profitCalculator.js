/**
 * Clean Profit Calculator System
 * Completely new calculation system to fix daily report issues
 */

/**
 * Calculate cost of goods sold for given invoices and products
 * @param {Array} invoices - Array of invoice objects
 * @param {Array} products - Array of product objects
 * @returns {Object} Cost calculation result
 */
export function calculateCostOfGoodsSold(invoices, products) {
  console.log('🧮 Starting cost calculation...');
  
  let totalCost = 0;
  let processedItems = 0;
  const missingProducts = [];
  const invalidProducts = [];
  const processedDetails = [];

  // Create product lookup map
  const productMap = {};
  products.forEach(product => {
    if (product && product.id) {
      const buyPrice = parseFloat(product.buyPrice) || 0;
      const sellPrice = parseFloat(product.sellPrice || product.price) || 0;
      
      if (buyPrice > 0 && sellPrice > 0) {
        productMap[product.id] = {
          id: product.id,
          name: product.name || product.id,
          buyPrice: buyPrice,
          sellPrice: sellPrice
        };
      } else {
        invalidProducts.push({
          id: product.id,
          name: product.name || product.id,
          buyPrice: buyPrice,
          sellPrice: sellPrice
        });
      }
    }
  });

  console.log(`📦 Valid products: ${Object.keys(productMap).length}`);
  console.log(`⚠️ Invalid products: ${invalidProducts.length}`);

  // Process invoices
  invoices.forEach((invoice, invoiceIndex) => {
    if (!invoice || !invoice.items || !Array.isArray(invoice.items)) {
      console.warn(`Invoice ${invoiceIndex} has no valid items`);
      return;
    }

    console.log(`📄 Processing invoice ${invoice.invoiceNumber || invoice.id || invoiceIndex}`);

    invoice.items.forEach((item, itemIndex) => {
      if (!item || !item.productId) {
        console.warn(`Item ${itemIndex} in invoice ${invoiceIndex} has no product ID`);
        return;
      }

      const product = productMap[item.productId];
      if (!product) {
        missingProducts.push({
          productId: item.productId,
          invoiceId: invoice.invoiceNumber || invoice.id || invoiceIndex,
          quantity: item.quantity
        });
        console.warn(`Product ${item.productId} not found`);
        return;
      }

      const quantity = parseFloat(item.quantity) || 0;
      if (quantity <= 0) {
        console.warn(`Invalid quantity ${quantity} for product ${item.productId}`);
        return;
      }

      const itemCost = product.buyPrice * quantity;
      totalCost += itemCost;
      processedItems++;

      processedDetails.push({
        productId: product.id,
        productName: product.name,
        buyPrice: product.buyPrice,
        quantity: quantity,
        itemCost: itemCost
      });

      console.log(`✅ ${product.name}: ${quantity} × ${product.buyPrice} = ${itemCost}`);
    });
  });

  const result = {
    totalCost: parseFloat(totalCost.toFixed(2)),
    processedItems: processedItems,
    missingProducts: missingProducts,
    invalidProducts: invalidProducts,
    processedDetails: processedDetails,
    summary: {
      validProducts: Object.keys(productMap).length,
      invalidProducts: invalidProducts.length,
      missingProducts: missingProducts.length,
      processedItems: processedItems
    }
  };

  console.log('💰 Cost calculation complete:', result.totalCost);
  return result;
}

/**
 * Calculate daily expenses for a specific date
 * @param {Array} expenses - Array of expense objects
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Object} Expense calculation result
 */
export function calculateDailyExpenses(expenses, date) {
  console.log(`💸 Calculating expenses for ${date}...`);
  
  const dailyExpenses = expenses.filter(expense => {
    if (!expense || !expense.date) return false;
    
    // Normalize date format
    const expenseDate = new Date(expense.date).toISOString().split('T')[0];
    return expenseDate === date;
  });

  const totalExpenses = dailyExpenses.reduce((sum, expense) => {
    const amount = parseFloat(expense.amount) || 0;
    return sum + amount;
  }, 0);

  const result = {
    totalExpenses: parseFloat(totalExpenses.toFixed(2)),
    expenseCount: dailyExpenses.length,
    expenses: dailyExpenses.map(exp => ({
      id: exp.id,
      description: exp.description || 'No description',
      amount: parseFloat(exp.amount) || 0,
      date: exp.date
    }))
  };

  console.log(`💸 Daily expenses: ${result.totalExpenses} (${result.expenseCount} items)`);
  return result;
}

/**
 * Generate complete daily report with accurate profit calculations
 * @param {Array} invoices - Array of invoice objects
 * @param {Array} products - Array of product objects
 * @param {Array} expenses - Array of expense objects
 * @param {Date} date - Date for the report (defaults to today)
 * @returns {Object} Complete daily report
 */
export function generateCleanDailyReport(invoices, products, expenses, date = new Date()) {
  console.log('📊 === CLEAN DAILY REPORT GENERATION ===');
  
  // Normalize date
  const reportDate = date.toISOString().split('T')[0];
  console.log(`📅 Report date: ${reportDate}`);

  // Filter daily invoices
  const dailyInvoices = invoices.filter(invoice => {
    if (!invoice || !invoice.date) return false;
    const invoiceDate = new Date(invoice.date).toISOString().split('T')[0];
    return invoiceDate === reportDate;
  });

  console.log(`📄 Found ${dailyInvoices.length} invoices for ${reportDate}`);

  // Calculate total sales
  const totalSales = dailyInvoices.reduce((sum, invoice) => {
    const amount = parseFloat(invoice.finalTotal) || 0;
    return sum + amount;
  }, 0);

  console.log(`💰 Total sales: ${totalSales}`);

  // Calculate cost of goods sold
  const costResult = calculateCostOfGoodsSold(dailyInvoices, products);
  const costOfGoodsSold = costResult.totalCost;

  // Calculate daily expenses
  const expenseResult = calculateDailyExpenses(expenses, reportDate);
  const dailyExpenses = expenseResult.totalExpenses;

  // Calculate profits
  const grossProfit = totalSales - costOfGoodsSold;
  const netProfit = grossProfit - dailyExpenses;
  const profitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0;

  const report = {
    date: reportDate,
    totalSales: parseFloat(totalSales.toFixed(2)),
    costOfGoodsSold: parseFloat(costOfGoodsSold.toFixed(2)),
    dailyExpenses: parseFloat(dailyExpenses.toFixed(2)),
    grossProfit: parseFloat(grossProfit.toFixed(2)),
    netProfit: parseFloat(netProfit.toFixed(2)),
    profitMargin: parseFloat(profitMargin.toFixed(2)),
    
    // Counts
    invoiceCount: dailyInvoices.length,
    expenseCount: expenseResult.expenseCount,
    processedItems: costResult.processedItems,
    
    // Details
    costDetails: costResult,
    expenseDetails: expenseResult,
    
    // Validation
    isValid: dailyInvoices.length > 0,
    hasErrors: costResult.missingProducts.length > 0,
    warnings: [
      ...costResult.invalidProducts.map(p => `Product ${p.name} has invalid prices`),
      ...costResult.missingProducts.map(m => `Product ${m.productId} not found`)
    ]
  };

  console.log('📊 === DAILY REPORT SUMMARY ===');
  console.log(`Total Sales: ${report.totalSales}`);
  console.log(`Cost of Goods Sold: ${report.costOfGoodsSold}`);
  console.log(`Daily Expenses: ${report.dailyExpenses}`);
  console.log(`Gross Profit: ${report.grossProfit}`);
  console.log(`Net Profit: ${report.netProfit}`);
  console.log(`Profit Margin: ${report.profitMargin}%`);
  console.log('📊 === END REPORT ===');

  return report;
}

/**
 * Simple validation function to check if net profit calculation is correct
 * @param {Object} report - Daily report object
 * @returns {boolean} True if calculation is correct
 */
export function validateDailyReport(report) {
  const calculatedGrossProfit = report.totalSales - report.costOfGoodsSold;
  const calculatedNetProfit = calculatedGrossProfit - report.dailyExpenses;
  
  const grossProfitCorrect = Math.abs(report.grossProfit - calculatedGrossProfit) < 0.01;
  const netProfitCorrect = Math.abs(report.netProfit - calculatedNetProfit) < 0.01;
  
  if (!grossProfitCorrect) {
    console.error(`Gross profit calculation error: Expected ${calculatedGrossProfit}, got ${report.grossProfit}`);
  }
  
  if (!netProfitCorrect) {
    console.error(`Net profit calculation error: Expected ${calculatedNetProfit}, got ${report.netProfit}`);
  }
  
  return grossProfitCorrect && netProfitCorrect;
}
