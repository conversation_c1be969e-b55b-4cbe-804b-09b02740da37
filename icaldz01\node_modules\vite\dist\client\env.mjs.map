{"version": 3, "file": "env.mjs", "sources": ["env.ts"], "sourcesContent": ["declare const __MODE__: string\ndeclare const __DEFINES__: Record<string, any>\n\nconst context = (() => {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis\n  } else if (typeof self !== 'undefined') {\n    return self\n  } else if (typeof window !== 'undefined') {\n    return window\n  } else {\n    return Function('return this')()\n  }\n})()\n\n// assign defines\nconst defines = __DEFINES__\nObject.keys(defines).forEach((key) => {\n  const segments = key.split('.')\n  let target = context\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i]\n    if (i === segments.length - 1) {\n      target[segment] = defines[key]\n    } else {\n      target = target[segment] || (target[segment] = {})\n    }\n  }\n})\n"], "names": [], "mappings": "AAGA,MAAM,OAAO,GAAG,CAAC,MAAK;AACpB,IAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACrC,QAAA,OAAO,UAAU,CAAA;AAClB,KAAA;AAAM,SAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AACtC,QAAA,OAAO,IAAI,CAAA;AACZ,KAAA;AAAM,SAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACxC,QAAA,OAAO,MAAM,CAAA;AACd,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;AACjC,KAAA;AACH,CAAC,GAAG,CAAA;AAEJ;AACA,MAAM,OAAO,GAAG,WAAW,CAAA;AAC3B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;IACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC/B,IAAI,MAAM,GAAG,OAAO,CAAA;AACpB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;AAC/B,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;AACnD,SAAA;AACF,KAAA;AACH,CAAC,CAAC", "x_google_ignoreList": [0]}