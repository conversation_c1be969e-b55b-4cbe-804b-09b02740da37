/**
 * Algerian Locale Configuration
 * Contains month names and other locale-specific settings for Algeria
 */

// Algerian month names (as requested by user)
export const ALGERIAN_MONTHS = [
  'جانفي',    // January
  'فيفري',    // February (Algerian dialect)
  'مارس',     // March
  'أفريل',    // April
  'ماي',      // May
  'جوان',     // June
  'جويلية',   // July
  'أوت',      // August
  'سبتمبر',   // September
  'أكتوبر',   // October
  'نوفمبر',   // November
  'ديسمبر'    // December
];

// Algerian day names
export const ALGERIAN_DAYS = [
  'الأحد',     // Sunday
  'الاثنين',   // Monday
  'الثلاثاء',  // Tuesday
  'الأربعاء',  // Wednesday
  'الخميس',   // Thursday
  'الجمعة',   // Friday
  'السبت'     // Saturday
];

// Short day names
export const ALGERIAN_DAYS_SHORT = [
  'أحد',
  'اثنين',
  'ثلاثاء',
  'أربعاء',
  'خميس',
  'جمعة',
  'سبت'
];

/**
 * Get Algerian month name by index (0-based)
 * @param {number} monthIndex - Month index (0-11)
 * @returns {string} Algerian month name
 */
export function getAlgerianMonth(monthIndex) {
  if (monthIndex < 0 || monthIndex > 11) {
    return 'شهر غير صحيح';
  }
  return ALGERIAN_MONTHS[monthIndex];
}

/**
 * Get Algerian day name by index (0-based, Sunday = 0)
 * @param {number} dayIndex - Day index (0-6)
 * @returns {string} Algerian day name
 */
export function getAlgerianDay(dayIndex) {
  if (dayIndex < 0 || dayIndex > 6) {
    return 'يوم غير صحيح';
  }
  return ALGERIAN_DAYS[dayIndex];
}

/**
 * Format date using Algerian month names
 * @param {Date} date - Date object
 * @returns {string} Formatted date string
 */
export function formatAlgerianDate(date) {
  const day = date.getDate();
  const month = getAlgerianMonth(date.getMonth());
  const year = date.getFullYear();
  return `${day} ${month} ${year}`;
}

/**
 * Format date with day name using Algerian locale
 * @param {Date} date - Date object
 * @returns {string} Formatted date string with day name
 */
export function formatAlgerianDateWithDay(date) {
  const dayName = getAlgerianDay(date.getDay());
  const formattedDate = formatAlgerianDate(date);
  return `${dayName}، ${formattedDate}`;
}

/**
 * Get current month name in Algerian dialect
 * @returns {string} Current month name
 */
export function getCurrentAlgerianMonth() {
  return getAlgerianMonth(new Date().getMonth());
}

/**
 * Get current year in Arabic numerals
 * @returns {string} Current year
 */
export function getCurrentAlgerianYear() {
  return new Date().getFullYear().toString();
}

// Export default configuration
export default {
  months: ALGERIAN_MONTHS,
  days: ALGERIAN_DAYS,
  daysShort: ALGERIAN_DAYS_SHORT,
  getMonth: getAlgerianMonth,
  getDay: getAlgerianDay,
  formatDate: formatAlgerianDate,
  formatDateWithDay: formatAlgerianDateWithDay,
  getCurrentMonth: getCurrentAlgerianMonth,
  getCurrentYear: getCurrentAlgerianYear
};
