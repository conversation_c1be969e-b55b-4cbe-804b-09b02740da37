# 🔧 المواصفات التقنية لنظام iCalDZ
# iCalDZ Technical Specifications

## 🏗️ البنية التقنية / Technical Architecture

### 🖥️ تقنيات الواجهة الأمامية / Frontend Technologies
- **⚛️ React 18.2.0**: مكتبة واجهة المستخدم الحديثة
- **🎨 CSS3 المتقدم**: تصميم متجاوب وحديث
- **📱 Responsive Design**: تصميم متكيف مع جميع الأجهزة
- **🌍 Multi-Language Support**: دعم RTL/LTR للغات متعددة
- **⚡ Vite 4.4.5**: أداة بناء سريعة وحديثة

### 🔧 تقنيات الخلفية / Backend Technologies
- **🖥️ Electron 22.0.0**: تطبيق سطح مكتب متعدد المنصات
- **🟢 Node.js**: بيئة تشغيل JavaScript
- **🚀 Express 4.18.2**: إطار عمل خادم الويب
- **🔒 Crypto-JS 4.2.0**: تشفير وحماية البيانات
- **📊 XLSX 0.18.5**: معالجة ملفات Excel

### 💾 إدارة البيانات / Data Management
- **🗄️ LocalStorage**: تخزين محلي في المتصفح
- **📋 JSON**: تنسيق تبادل البيانات
- **📊 Excel Integration**: استيراد وتصدير Excel
- **🔄 Data Sync**: نظام مزامنة البيانات
- **💾 Backup System**: نظام النسخ الاحتياطية

## 🔐 نظام الأمان والتشفير / Security & Encryption System

### 🛡️ آليات الحماية / Protection Mechanisms
- **🔒 AES-256 Encryption**: تشفير قوي للبيانات الحساسة
- **🖥️ Machine Fingerprinting**: بصمة فريدة لكل جهاز
- **🔑 One-Time Activation**: تفعيل لمرة واحدة فقط
- **⏰ Time-Based Validation**: التحقق المبني على الوقت
- **🚫 Anti-Tampering**: حماية من التلاعب

### 🔍 خوارزميات التشفير / Encryption Algorithms
```javascript
// مثال على تشفير البيانات
const encryptedData = CryptoJS.AES.encrypt(
  JSON.stringify(activationData), 
  secretKey
).toString();

// إنشاء بصمة الجهاز
const machineFingerprint = CryptoJS.SHA256(
  navigator.userAgent + 
  navigator.platform + 
  screen.width + 'x' + screen.height
).toString().substring(0, 16);
```

### 🔐 نظام التفعيل / Activation System
- **🎯 Builder Pattern**: نمط البناء لتوليد الأكواد
- **⏱️ Trial Codes**: أكواد تجربة مؤقتة (1-7 أيام)
- **♾️ Lifetime Codes**: أكواد تفعيل دائمة
- **🔒 Secure Validation**: التحقق الآمن من الأكواد
- **📱 Device Binding**: ربط الكود بالجهاز

## 🌍 نظام اللغات المتعدد / Multi-Language System

### 📝 ملفات الترجمة / Translation Files
```javascript
// هيكل ملف الترجمة
export const translations = {
  ar: {
    dashboard: "لوحة التحكم",
    sales: "المبيعات",
    products: "المنتجات"
    // ... المزيد من الترجمات
  },
  fr: {
    dashboard: "Tableau de bord",
    sales: "Ventes", 
    products: "Produits"
    // ... المزيد من الترجمات
  },
  en: {
    dashboard: "Dashboard",
    sales: "Sales",
    products: "Products"
    // ... المزيد من الترجمات
  }
};
```

### 🎨 تكوين التخطيط / Layout Configuration
```javascript
export const LANGUAGES = {
  ar: {
    direction: 'rtl',
    sidebarPosition: 'right',
    floatingButtonsPosition: 'left',
    textAlign: 'right'
  },
  fr: {
    direction: 'ltr',
    sidebarPosition: 'left', 
    floatingButtonsPosition: 'right',
    textAlign: 'left'
  },
  en: {
    direction: 'ltr',
    sidebarPosition: 'left',
    floatingButtonsPosition: 'right', 
    textAlign: 'left'
  }
};
```

## 📊 هيكل قاعدة البيانات / Database Structure

### 🛒 جدول المبيعات / Sales Table
```javascript
const salesInvoice = {
  id: "INV-2025-001",
  date: "2025-01-15",
  customerId: "CUST-001",
  customerName: "أحمد محمد",
  paymentMethod: "نقداً",
  items: [
    {
      productId: "PROD-001",
      productName: "منتج تجريبي",
      quantity: 2,
      price: 100.00,
      total: 200.00
    }
  ],
  subtotal: 200.00,
  discount: 10.00,
  tax: 36.10,
  finalTotal: 226.10,
  createdAt: "2025-01-15T10:30:00Z"
};
```

### 📦 جدول المنتجات / Products Table
```javascript
const product = {
  id: "PROD-001",
  name: "منتج تجريبي",
  barcode: "1234567890123",
  category: "إلكترونيات",
  buyPrice: 80.00,
  sellPrice: 100.00,
  stock: 50,
  minStock: 10,
  description: "وصف المنتج",
  createdAt: "2025-01-01T00:00:00Z",
  updatedAt: "2025-01-15T10:30:00Z"
};
```

### 👥 جدول العملاء / Customers Table
```javascript
const customer = {
  id: "CUST-001",
  name: "أحمد محمد",
  phone: "+213555123456",
  email: "<EMAIL>",
  address: "الجزائر العاصمة",
  company: "شركة الاختبار",
  balance: 1500.00,
  creditLimit: 5000.00,
  discountRate: 5.0,
  paymentTerm: 30,
  status: "نشط",
  createdAt: "2025-01-01T00:00:00Z"
};
```

## ⌨️ نظام الاختصارات / Shortcuts System

### 🎯 مدير الاختصارات / Shortcuts Manager
```javascript
export class KeyboardShortcuts {
  constructor() {
    this.shortcuts = {
      'F1': () => this.openSalesInvoice(),
      'F3': () => this.addProduct(),
      'F6': () => this.newProduct(),
      'F7': () => this.openPurchaseInvoice(),
      'Enter': () => this.saveInvoice(),
      'Shift+Enter': () => this.saveAndPrint(),
      'Escape': () => this.closeModal()
    };
  }

  init() {
    document.addEventListener('keydown', this.handleKeyPress.bind(this));
  }

  handleKeyPress(event) {
    const key = this.getKeyString(event);
    if (this.shortcuts[key]) {
      event.preventDefault();
      this.shortcuts[key]();
    }
  }
}
```

## 🎵 نظام الصوت / Sound System

### 🔊 مدير الأصوات / Sound Manager
```javascript
export class SoundManager {
  static sounds = {
    success: '/sounds/success.mp3',
    error: '/sounds/error.mp3',
    warning: '/sounds/warning.mp3',
    newInvoice: '/sounds/new-invoice.mp3',
    saveInvoice: '/sounds/save-invoice.mp3',
    print: '/sounds/print.mp3'
  };

  static async init() {
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    this.isEnabled = localStorage.getItem('soundEnabled') !== 'false';
  }

  static async play(soundName, options = {}) {
    if (!this.isEnabled) return;
    
    try {
      const audio = new Audio(this.sounds[soundName]);
      audio.volume = options.volume || 0.5;
      await audio.play();
    } catch (error) {
      console.warn('Could not play sound:', error);
    }
  }
}
```

## 📊 نظام التقارير / Reporting System

### 📈 مولد التقارير / Report Generator
```javascript
export function generateCleanDailyReport(invoices, products, expenses, date) {
  const report = {
    date: date.toISOString().split('T')[0],
    totalSales: 0,
    costOfGoodsSold: 0,
    grossProfit: 0,
    totalExpenses: 0,
    netProfit: 0,
    invoiceCount: 0,
    processedItems: 0,
    warnings: [],
    hasErrors: false
  };

  // حساب إجمالي المبيعات
  const todayInvoices = invoices.filter(inv => inv.date === report.date);
  report.totalSales = todayInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);
  report.invoiceCount = todayInvoices.length;

  // حساب تكلفة البضاعة المباعة
  const costCalculation = calculateCostOfGoodsSold(todayInvoices, products);
  report.costOfGoodsSold = costCalculation.totalCost;
  report.processedItems = costCalculation.processedItems;

  // حساب الأرباح
  report.grossProfit = report.totalSales - report.costOfGoodsSold;
  
  // حساب المصاريف
  const todayExpenses = expenses.filter(exp => exp.date === report.date);
  report.totalExpenses = todayExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);
  
  // صافي الربح
  report.netProfit = report.grossProfit - report.totalExpenses;

  return report;
}
```

## 🖨️ نظام الطباعة / Printing System

### 🧾 تكوين الطباعة / Print Configuration
```javascript
export const printConfig = {
  thermal: {
    width: '80mm',
    fontSize: '12px',
    fontFamily: 'monospace',
    lineHeight: '1.2'
  },
  a4: {
    width: '210mm',
    height: '297mm',
    margin: '20mm',
    fontSize: '14px',
    fontFamily: 'Arial, sans-serif'
  },
  receipt: {
    width: '58mm',
    fontSize: '10px',
    fontFamily: 'monospace',
    lineHeight: '1.1'
  }
};

export function printInvoice(invoice, format = 'thermal') {
  const config = printConfig[format];
  const printWindow = window.open('', '_blank');
  
  const html = generateInvoiceHTML(invoice, config);
  printWindow.document.write(html);
  printWindow.document.close();
  printWindow.print();
}
```

## 💾 نظام النسخ الاحتياطية / Backup System

### 🔄 مدير المزامنة / Sync Manager
```javascript
export class DataSyncManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.lastSyncTime = localStorage.getItem('last-sync-time');
    this.syncInterval = 5 * 60 * 1000; // 5 دقائق
  }

  async syncAllData() {
    const dataKeys = [
      'icaldz-products',
      'icaldz-customers', 
      'icaldz-invoices',
      'icaldz-purchases',
      'icaldz-suppliers',
      'icaldz-expenses'
    ];

    for (const key of dataKeys) {
      try {
        await this.syncDataKey(key);
      } catch (error) {
        console.warn(`فشل في مزامنة ${key}:`, error);
      }
    }
  }

  exportToJSON() {
    const data = {};
    const keys = Object.keys(localStorage).filter(key => key.startsWith('icaldz-'));
    
    keys.forEach(key => {
      try {
        data[key] = JSON.parse(localStorage.getItem(key));
      } catch (error) {
        console.warn(`خطأ في تصدير ${key}:`, error);
      }
    });

    return JSON.stringify(data, null, 2);
  }
}
```

## 📱 تكامل الأجهزة المحمولة / Mobile Integration

### 📲 تكوين PWA / PWA Configuration
```javascript
// manifest.json
{
  "name": "iCalDZ Accounting System",
  "short_name": "iCalDZ",
  "description": "نظام محاسبي متكامل",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#177e89",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png", 
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## 🔧 متطلبات النظام / System Requirements

### 💻 الحد الأدنى / Minimum Requirements
- **🖥️ نظام التشغيل**: Windows 10, macOS 10.14, Ubuntu 18.04
- **💾 الذاكرة**: 4GB RAM
- **💿 مساحة القرص**: 500MB
- **🌐 المتصفح**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **📶 الاتصال**: اتصال إنترنت للتفعيل والتحديثات

### ⚡ الموصى به / Recommended
- **🖥️ نظام التشغيل**: Windows 11, macOS 12+, Ubuntu 20.04+
- **💾 الذاكرة**: 8GB RAM أو أكثر
- **💿 مساحة القرص**: 2GB مساحة فارغة
- **🌐 المتصفح**: أحدث إصدار
- **📶 الاتصال**: اتصال إنترنت سريع

---

**© 2025 iCode DZ - جميع الحقوق محفوظة / All Rights Reserved**
