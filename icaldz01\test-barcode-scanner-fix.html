<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Barcode Scanner Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #28a745;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .barcode-input {
            background: linear-gradient(135deg, #0f1419, #1a252f);
            color: #00ff41;
            border: 2px solid #00ff41;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Barcode Scanner Fix Test</h1>
        <p>This page tests the fixed keyboard shortcuts system to ensure barcode scanners work correctly.</p>

        <div class="test-section">
            <h2>📊 System Status</h2>
            <div id="systemStatus" class="status-panel">
                Loading system status...
            </div>
            <button class="test-button" onclick="updateSystemStatus()">🔄 Refresh Status</button>
            <button class="test-button" onclick="toggleBarcodeProtection()">🛡️ Toggle Barcode Protection</button>
            <button class="test-button" onclick="toggleKeyboardShortcuts()">⌨️ Toggle Shortcuts</button>
        </div>

        <div class="test-section">
            <h2>📷 Barcode Input Tests</h2>
            <p>Test different types of barcode input fields:</p>
            
            <h3>Dashboard Scanner (should be protected)</h3>
            <input type="text" class="test-input barcode-input" placeholder="Dashboard barcode scanner - امسح الباركود" 
                   id="dashboard-scanner" onkeydown="logKeyEvent(event, 'Dashboard Scanner')" />
            
            <h3>Sales Scanner (should be protected)</h3>
            <input type="text" class="test-input barcode-input" placeholder="Sales barcode scanner - مسح منتج للفاتورة" 
                   class="sales-scanner barcode-input" onkeydown="logKeyEvent(event, 'Sales Scanner')" />
            
            <h3>Product Barcode (should be protected)</h3>
            <input type="text" class="test-input barcode-input" placeholder="Product barcode - باركود المنتج" 
                   id="product-barcode" onkeydown="logKeyEvent(event, 'Product Barcode')" />
            
            <h3>Regular Input (should NOT be protected)</h3>
            <input type="text" class="test-input" placeholder="Regular input field - حقل إدخال عادي" 
                   onkeydown="logKeyEvent(event, 'Regular Input')" />
        </div>

        <div class="test-section">
            <h2>⌨️ Keyboard Shortcut Tests</h2>
            <p>Test keyboard shortcuts (should work when NOT in barcode fields):</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                <button class="test-button" onclick="testShortcut('F1')">F1 Test</button>
                <button class="test-button" onclick="testShortcut('F2')">F2 Test</button>
                <button class="test-button" onclick="testShortcut('F3')">F3 Test</button>
                <button class="test-button" onclick="testShortcut('F5')">F5 Test</button>
                <button class="test-button" onclick="testShortcut('Escape')">ESC Test</button>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Event Log</h2>
            <div id="eventLog" class="status-panel" style="height: 200px; overflow-y: auto;">
                Event log will appear here...
            </div>
            <button class="test-button" onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div class="test-section">
            <h2>🧪 Test Results</h2>
            <div id="testResults" class="test-result info">
                Run tests to see results...
            </div>
        </div>
    </div>

    <script>
        // Import the fixed KeyboardShortcuts (simulated for testing)
        const KeyboardShortcuts = {
            isEnabled: true,
            barcodeProtectionEnabled: true,
            activeWindow: 'dashboard',
            
            setEnabled(enabled) {
                this.isEnabled = enabled;
                log(`⌨️ Keyboard shortcuts ${enabled ? 'enabled' : 'disabled'}`);
            },
            
            setBarcodeProtection(enabled) {
                this.barcodeProtectionEnabled = enabled;
                log(`🛡️ Barcode protection ${enabled ? 'enabled' : 'disabled'}`);
            },
            
            getStatus() {
                return {
                    isEnabled: this.isEnabled,
                    barcodeProtectionEnabled: this.barcodeProtectionEnabled,
                    activeWindow: this.activeWindow
                };
            },
            
            isBarcodeInputActive(event) {
                if (!this.barcodeProtectionEnabled) return false;
                
                const target = event.target;
                if (!target || target.tagName !== 'INPUT') return false;
                
                const barcodeIndicators = [
                    'barcode-input', 'scanner-input', 'dashboard-scanner',
                    'sales-scanner', 'edit-scanner', 'product-barcode'
                ];
                
                if (target.className) {
                    const classNames = target.className.toLowerCase();
                    if (barcodeIndicators.some(indicator => classNames.includes(indicator))) {
                        return true;
                    }
                }
                
                if (target.id) {
                    const id = target.id.toLowerCase();
                    if (barcodeIndicators.some(indicator => id.includes(indicator))) {
                        return true;
                    }
                }
                
                if (target.placeholder) {
                    const placeholder = target.placeholder.toLowerCase();
                    const barcodeKeywords = ['barcode', 'باركود', 'scanner', 'مسح', 'scan'];
                    if (barcodeKeywords.some(keyword => placeholder.includes(keyword))) {
                        return true;
                    }
                }
                
                return false;
            }
        };

        function log(message) {
            const logElement = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function logKeyEvent(event, source) {
            const isBarcodeInput = KeyboardShortcuts.isBarcodeInputActive(event);
            const keyInfo = `${event.key} (${event.code})`;
            
            log(`🔍 ${source}: ${keyInfo} - Barcode Input: ${isBarcodeInput ? '✅ YES' : '❌ NO'}`);
            
            if (isBarcodeInput) {
                log(`🛡️ Barcode protection active - shortcuts should be disabled`);
            } else {
                log(`⌨️ Regular input - shortcuts should work normally`);
            }
        }

        function testShortcut(key) {
            log(`🧪 Testing shortcut: ${key}`);
            // Simulate shortcut execution
            setTimeout(() => {
                log(`✅ Shortcut ${key} executed successfully`);
            }, 100);
        }

        function updateSystemStatus() {
            const status = KeyboardShortcuts.getStatus();
            document.getElementById('systemStatus').innerHTML = `
System Status:
- Keyboard Shortcuts: ${status.isEnabled ? '✅ Enabled' : '❌ Disabled'}
- Barcode Protection: ${status.barcodeProtectionEnabled ? '🛡️ Enabled' : '⚠️ Disabled'}
- Active Window: ${status.activeWindow}
- Current Time: ${new Date().toLocaleString()}
            `;
            log('📊 System status updated');
        }

        function toggleBarcodeProtection() {
            const newState = !KeyboardShortcuts.barcodeProtectionEnabled;
            KeyboardShortcuts.setBarcodeProtection(newState);
            updateSystemStatus();
        }

        function toggleKeyboardShortcuts() {
            const newState = !KeyboardShortcuts.isEnabled;
            KeyboardShortcuts.setEnabled(newState);
            updateSystemStatus();
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
            log('🗑️ Event log cleared');
        }

        // Initialize
        log('🧪 Barcode Scanner Fix Test initialized');
        updateSystemStatus();
    </script>
</body>
</html>
