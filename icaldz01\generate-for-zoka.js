/**
 * Generate Enhanced Security Codes for Zoka
 * Quick generation script
 */

const EnhancedGenerator = require('./generate-enhanced-activation-code');

console.log('🔐 Enhanced Security Code Generator for Zoka');
console.log('=============================================');

const generator = new EnhancedGenerator();

// Generate Basic Security Code
console.log('\n1. 🔒 Generating BASIC Security Code...');
const basicCode = generator.generateEnhancedActivationCode({
  clientName: 'zoka',
  machineId: 'F7C681E2C5959036',
  securityLevel: 'BASIC',
  type: 'LIFETIME',
  hardwareBinding: false,
  maxDevices: 1
});

if (basicCode) {
  console.log('✅ BASIC Code Generated Successfully!');
  console.log('=====================================');
  console.log('🔑 Activation Code:', basicCode.activationCode);
  console.log('👤 Client:', basicCode.clientName);
  console.log('🆔 Client ID:', basicCode.clientId);
  console.log('🔒 Security Level:', basicCode.securityLevel);
  console.log('📅 Generated:', basicCode.generatedAt);
  console.log('💻 Machine ID Bound:', basicCode.securityFeatures.machineBinding ? 'YES' : 'NO');
  console.log('🔧 Hardware Binding:', basicCode.securityFeatures.hardwareBinding ? 'YES' : 'NO');
  console.log('📱 Max Devices:', basicCode.securityFeatures.maxDevices);
  console.log('🔐 Server Token:', basicCode.serverToken);
} else {
  console.log('❌ Failed to generate basic code');
}

// Generate Enhanced Security Code
console.log('\n2. 🛡️ Generating ENHANCED Security Code...');
const enhancedCode = generator.generateEnhancedActivationCode({
  clientName: 'zoka',
  machineId: 'F7C681E2C5959036',
  securityLevel: 'ENHANCED',
  type: 'LIFETIME',
  allowedIPs: [], // No IP restrictions for now
  maxDevices: 1,
  hardwareBinding: true,
  networkValidation: true
});

if (enhancedCode) {
  console.log('✅ ENHANCED Code Generated Successfully!');
  console.log('========================================');
  console.log('🔑 Activation Code:', enhancedCode.activationCode);
  console.log('👤 Client:', enhancedCode.clientName);
  console.log('🆔 Client ID:', enhancedCode.clientId);
  console.log('🔒 Security Level:', enhancedCode.securityLevel);
  console.log('📅 Generated:', enhancedCode.generatedAt);
  console.log('💻 Machine ID Bound:', enhancedCode.securityFeatures.machineBinding ? 'YES' : 'NO');
  console.log('🔧 Hardware Binding:', enhancedCode.securityFeatures.hardwareBinding ? 'YES' : 'NO');
  console.log('🌐 IP Restrictions:', enhancedCode.securityFeatures.ipRestriction ? 'YES' : 'NO');
  console.log('🔗 Network Validation:', enhancedCode.securityFeatures.networkValidation ? 'YES' : 'NO');
  console.log('📱 Max Devices:', enhancedCode.securityFeatures.maxDevices);
  console.log('🔐 Server Token:', enhancedCode.serverToken);
} else {
  console.log('❌ Failed to generate enhanced code');
}

// Generate Maximum Security Code
console.log('\n3. 🔐 Generating MAXIMUM Security Code...');
const maximumCode = generator.generateEnhancedActivationCode({
  clientName: 'zoka',
  machineId: 'F7C681E2C5959036',
  securityLevel: 'MAXIMUM',
  type: 'LIFETIME',
  allowedIPs: [], // No IP restrictions for now
  maxDevices: 1,
  geoRestriction: null, // No geo restrictions for now
  hardwareBinding: true,
  behaviorTracking: true,
  networkValidation: true,
  continuousMonitoring: true
});

if (maximumCode) {
  console.log('✅ MAXIMUM Security Code Generated Successfully!');
  console.log('===============================================');
  console.log('🔑 Activation Code:', maximumCode.activationCode);
  console.log('👤 Client:', maximumCode.clientName);
  console.log('🆔 Client ID:', maximumCode.clientId);
  console.log('🔒 Security Level:', maximumCode.securityLevel);
  console.log('📅 Generated:', maximumCode.generatedAt);
  console.log('💻 Machine ID Bound: YES');
  console.log('🔧 Hardware Binding: YES');
  console.log('🌐 IP Restrictions:', maximumCode.securityFeatures.ipRestriction ? 'YES' : 'NO');
  console.log('🌍 Geo Restrictions:', maximumCode.securityFeatures.geoRestriction ? 'YES' : 'NO');
  console.log('🧠 Behavior Tracking: YES');
  console.log('🔗 Network Validation: YES');
  console.log('🔄 Continuous Monitoring: YES');
  console.log('📱 Max Devices:', maximumCode.securityFeatures.maxDevices);
  console.log('🔐 Server Token:', maximumCode.serverToken);
} else {
  console.log('❌ Failed to generate maximum security code');
}

// Generate Trial Code
console.log('\n4. 🧪 Generating 7-Day TRIAL Code...');
const trialCode = generator.generateEnhancedActivationCode({
  clientName: 'zoka-trial',
  securityLevel: 'ENHANCED',
  type: 'TRIAL',
  trialDays: 7,
  hardwareBinding: true,
  networkValidation: true
});

if (trialCode) {
  console.log('✅ TRIAL Code Generated Successfully!');
  console.log('====================================');
  console.log('🔑 Activation Code:', trialCode.activationCode);
  console.log('👤 Client:', trialCode.clientName);
  console.log('📅 Trial Duration: 7 days');
  console.log('⏰ Expires:', trialCode.expiryDate);
  console.log('🔒 Security Level:', trialCode.securityLevel);
  console.log('🔐 Server Token:', trialCode.serverToken);
} else {
  console.log('❌ Failed to generate trial code');
}

console.log('\n🎉 Code Generation Complete!');
console.log('============================');

console.log('\n📋 SUMMARY - Your Enhanced Security Codes:');
console.log('==========================================');

if (basicCode) {
  console.log('🔒 BASIC Security:');
  console.log(`   Code: ${basicCode.activationCode}`);
  console.log(`   Features: Machine ID binding, Basic encryption`);
}

if (enhancedCode) {
  console.log('\n🛡️ ENHANCED Security:');
  console.log(`   Code: ${enhancedCode.activationCode}`);
  console.log(`   Features: Hardware fingerprinting, Network validation, Multi-device management`);
}

if (maximumCode) {
  console.log('\n🔐 MAXIMUM Security:');
  console.log(`   Code: ${maximumCode.activationCode}`);
  console.log(`   Features: ALL security layers enabled - Ultimate protection`);
}

if (trialCode) {
  console.log('\n🧪 TRIAL Code (7 days):');
  console.log(`   Code: ${trialCode.activationCode}`);
  console.log(`   Expires: ${trialCode.expiryDate}`);
}

console.log('\n🛡️ Security Improvements Over Machine ID Only:');
console.log('==============================================');
console.log('✅ Hardware Fingerprinting - Unique hardware signature');
console.log('✅ Network Analysis - Network interface fingerprinting');
console.log('✅ Behavioral Tracking - User interaction patterns (Maximum level)');
console.log('✅ Multi-Layer Validation - Multiple security checks');
console.log('✅ Enhanced Format - More complex code structure');
console.log('✅ Server Validation - Backend verification tokens');
console.log('✅ Anti-Tampering - Code integrity protection');
console.log('✅ Continuous Monitoring - Real-time security (Maximum level)');

console.log('\n🔧 How to Use These Codes:');
console.log('=========================');
console.log('1. Choose the security level that fits your needs:');
console.log('   • BASIC: Similar to your current system but enhanced');
console.log('   • ENHANCED: Recommended for most users (multiple security layers)');
console.log('   • MAXIMUM: Enterprise-level security (all features enabled)');
console.log('');
console.log('2. Integrate the validation system into your application');
console.log('3. Each code can only be used once');
console.log('4. Store the server tokens for backend validation');

console.log('\n📞 Support: +213 551 93 05 89');
console.log('📧 Email: <EMAIL>');

console.log('\n🔐 Your activation codes are now protected by enterprise-level security!');
console.log('Even if someone gets your machine ID, they still need to bypass 7 other security layers.');
