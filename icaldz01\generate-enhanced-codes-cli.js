#!/usr/bin/env node

/**
 * Enhanced Activation Code Generator CLI
 * Command-line interface for generating secure activation codes
 * 
 * <AUTHOR> DZ
 * @version 2.0.0
 */

const EnhancedActivationCodeGenerator = require('./generate-enhanced-activation-code');
const readline = require('readline');

class EnhancedCodeGeneratorCLI {
  constructor() {
    this.generator = new EnhancedActivationCodeGenerator();
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  /**
   * Start the CLI interface
   */
  async start() {
    console.log('\n🔐 Enhanced Activation Code Generator v2.0');
    console.log('━'.repeat(50));
    console.log('Multiple Security Layers Beyond Machine ID');
    console.log('━'.repeat(50));
    
    await this.showMainMenu();
  }

  /**
   * Show main menu
   */
  async showMainMenu() {
    console.log('\n📋 Select Generation Type:');
    console.log('1. 🔒 Basic Security Code (Machine ID only)');
    console.log('2. 🛡️  Enhanced Security Code (Multiple layers)');
    console.log('3. 🔐 Maximum Security Code (All features)');
    console.log('4. 🧪 Trial Code (Time-limited)');
    console.log('5. 📊 View Generated Codes');
    console.log('6. ✅ Validate Code');
    console.log('7. ❌ Exit');
    
    const choice = await this.askQuestion('\n👉 Enter your choice (1-7): ');
    
    switch (choice.trim()) {
      case '1':
        await this.generateBasicCode();
        break;
      case '2':
        await this.generateEnhancedCode();
        break;
      case '3':
        await this.generateMaximumCode();
        break;
      case '4':
        await this.generateTrialCode();
        break;
      case '5':
        await this.viewGeneratedCodes();
        break;
      case '6':
        await this.validateCode();
        break;
      case '7':
        console.log('\n👋 Goodbye!');
        this.rl.close();
        return;
      default:
        console.log('❌ Invalid choice. Please try again.');
        await this.showMainMenu();
    }
  }

  /**
   * Generate basic security code
   */
  async generateBasicCode() {
    console.log('\n🔒 Generating Basic Security Code');
    console.log('━'.repeat(40));
    
    const clientName = await this.askQuestion('👤 Client name (or press Enter for default): ');
    const machineId = await this.askQuestion('💻 Machine ID (optional): ');
    
    const options = {
      clientName: clientName || 'Licensed User',
      machineId: machineId || null,
      securityLevel: 'BASIC',
      type: 'LIFETIME',
      hardwareBinding: false,
      maxDevices: 1
    };
    
    const result = this.generator.generateEnhancedActivationCode(options);
    
    if (result) {
      this.displayGeneratedCode(result);
    } else {
      console.log('❌ Failed to generate activation code');
    }
    
    await this.showMainMenu();
  }

  /**
   * Generate enhanced security code
   */
  async generateEnhancedCode() {
    console.log('\n🛡️ Generating Enhanced Security Code');
    console.log('━'.repeat(40));
    
    const clientName = await this.askQuestion('👤 Client name: ');
    const machineId = await this.askQuestion('💻 Machine ID (recommended): ');
    const allowedIPs = await this.askQuestion('🌐 Allowed IP addresses (comma-separated, optional): ');
    const maxDevices = await this.askQuestion('📱 Maximum devices (default: 1): ');
    
    const options = {
      clientName: clientName || 'Licensed User',
      machineId: machineId || null,
      securityLevel: 'ENHANCED',
      type: 'LIFETIME',
      allowedIPs: allowedIPs ? allowedIPs.split(',').map(ip => ip.trim()) : [],
      maxDevices: parseInt(maxDevices) || 1,
      hardwareBinding: true,
      networkValidation: true
    };
    
    const result = this.generator.generateEnhancedActivationCode(options);
    
    if (result) {
      this.displayGeneratedCode(result);
    } else {
      console.log('❌ Failed to generate activation code');
    }
    
    await this.showMainMenu();
  }

  /**
   * Generate maximum security code
   */
  async generateMaximumCode() {
    console.log('\n🔐 Generating Maximum Security Code');
    console.log('━'.repeat(40));
    
    const clientName = await this.askQuestion('👤 Client name: ');
    const machineId = await this.askQuestion('💻 Machine ID (required): ');
    const allowedIPs = await this.askQuestion('🌐 Allowed IP addresses (comma-separated): ');
    const maxDevices = await this.askQuestion('📱 Maximum devices (default: 1): ');
    const geoRestriction = await this.askQuestion('🌍 Geographic restriction (country code, optional): ');
    
    if (!machineId) {
      console.log('❌ Machine ID is required for maximum security');
      await this.showMainMenu();
      return;
    }
    
    const options = {
      clientName: clientName || 'Licensed User',
      machineId,
      securityLevel: 'MAXIMUM',
      type: 'LIFETIME',
      allowedIPs: allowedIPs ? allowedIPs.split(',').map(ip => ip.trim()) : [],
      maxDevices: parseInt(maxDevices) || 1,
      geoRestriction: geoRestriction || null,
      hardwareBinding: true,
      behaviorTracking: true,
      networkValidation: true,
      continuousMonitoring: true
    };
    
    const result = this.generator.generateEnhancedActivationCode(options);
    
    if (result) {
      this.displayGeneratedCode(result);
    } else {
      console.log('❌ Failed to generate activation code');
    }
    
    await this.showMainMenu();
  }

  /**
   * Generate trial code
   */
  async generateTrialCode() {
    console.log('\n🧪 Generating Trial Code');
    console.log('━'.repeat(40));
    
    const clientName = await this.askQuestion('👤 Client name: ');
    const trialDays = await this.askQuestion('📅 Trial days (1, 7, 30): ');
    const securityLevel = await this.askQuestion('🔒 Security level (BASIC/ENHANCED/MAXIMUM): ');
    
    const days = parseInt(trialDays) || 7;
    const level = ['BASIC', 'ENHANCED', 'MAXIMUM'].includes(securityLevel.toUpperCase()) 
      ? securityLevel.toUpperCase() 
      : 'ENHANCED';
    
    const options = {
      clientName: clientName || 'Trial User',
      securityLevel: level,
      type: 'TRIAL',
      trialDays: days,
      hardwareBinding: level !== 'BASIC',
      networkValidation: level !== 'BASIC',
      behaviorTracking: level === 'MAXIMUM',
      continuousMonitoring: level === 'MAXIMUM'
    };
    
    const result = this.generator.generateEnhancedActivationCode(options);
    
    if (result) {
      this.displayGeneratedCode(result);
    } else {
      console.log('❌ Failed to generate trial code');
    }
    
    await this.showMainMenu();
  }

  /**
   * Display generated code information
   */
  displayGeneratedCode(result) {
    console.log('\n✅ Activation Code Generated Successfully!');
    console.log('━'.repeat(50));
    console.log(`🔑 Activation Code: ${result.activationCode}`);
    console.log(`👤 Client: ${result.clientName}`);
    console.log(`🆔 Client ID: ${result.clientId}`);
    console.log(`📊 Type: ${result.type}`);
    console.log(`🔒 Security Level: ${result.securityLevel}`);
    console.log(`📅 Generated: ${result.generatedAt}`);
    
    if (result.expiryDate) {
      console.log(`⏰ Expires: ${result.expiryDate}`);
    }
    
    console.log('\n🛡️ Security Features:');
    const features = result.securityFeatures;
    console.log(`   • Machine Binding: ${features.machineBinding ? '✅' : '❌'}`);
    console.log(`   • IP Restriction: ${features.ipRestriction ? '✅' : '❌'}`);
    console.log(`   • Geo Restriction: ${features.geoRestriction ? '✅' : '❌'}`);
    console.log(`   • Hardware Binding: ${features.hardwareBinding ? '✅' : '❌'}`);
    console.log(`   • Max Devices: ${features.maxDevices}`);
    console.log(`   • Behavior Tracking: ${features.behaviorTracking ? '✅' : '❌'}`);
    console.log(`   • Network Validation: ${features.networkValidation ? '✅' : '❌'}`);
    console.log(`   • Continuous Monitoring: ${features.continuousMonitoring ? '✅' : '❌'}`);
    
    console.log('\n📋 Server Token (for validation):');
    console.log(`${result.serverToken}`);
    
    console.log('\n⚠️ IMPORTANT NOTES:');
    console.log('• Each code can only be used once');
    console.log('• Store the activation code securely');
    console.log('• Server token is used for backend validation');
    console.log('• Enhanced security features require proper client implementation');
  }

  /**
   * View generated codes
   */
  async viewGeneratedCodes() {
    console.log('\n📊 Generated Codes Database');
    console.log('━'.repeat(40));
    
    try {
      const fs = require('fs');
      const path = require('path');
      const dbPath = path.join(__dirname, 'used-codes-enhanced.json');
      
      if (!fs.existsSync(dbPath)) {
        console.log('📭 No codes generated yet');
        await this.showMainMenu();
        return;
      }
      
      const codes = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
      const codeList = Object.keys(codes);
      
      if (codeList.length === 0) {
        console.log('📭 No codes in database');
      } else {
        console.log(`📈 Total codes: ${codeList.length}\n`);
        
        codeList.slice(0, 10).forEach((code, index) => {
          const data = codes[code];
          console.log(`${index + 1}. ${code}`);
          console.log(`   Client: ${data.clientName}`);
          console.log(`   Type: ${data.type} | Security: Level ${data.securityLevel}`);
          console.log(`   Status: ${data.used ? '❌ Used' : '✅ Available'}`);
          console.log(`   Generated: ${data.generatedAt}`);
          console.log('');
        });
        
        if (codeList.length > 10) {
          console.log(`... and ${codeList.length - 10} more codes`);
        }
      }
    } catch (error) {
      console.log('❌ Error reading codes database:', error.message);
    }
    
    await this.showMainMenu();
  }

  /**
   * Validate code
   */
  async validateCode() {
    console.log('\n✅ Code Validation');
    console.log('━'.repeat(40));
    
    const code = await this.askQuestion('🔑 Enter activation code: ');
    const machineId = await this.askQuestion('💻 Machine ID (optional): ');
    
    const validationData = {
      machineId: machineId || null,
      ipAddress: '127.0.0.1', // Placeholder
      timestamp: Date.now()
    };
    
    const result = this.generator.validateEnhancedActivationCode(code, validationData);
    
    if (result.valid) {
      console.log('\n✅ Code is VALID');
      console.log(`📊 Type: ${result.data.type}`);
      console.log(`🔒 Security Level: ${result.data.securityLevel}`);
      console.log(`👤 Client: ${result.data.clientName}`);
      console.log(`📅 Generated: ${result.data.generatedAt}`);
      
      if (result.data.expiryDate) {
        console.log(`⏰ Expires: ${result.data.expiryDate}`);
      }
    } else {
      console.log('\n❌ Code is INVALID');
      console.log(`🚫 Reason: ${result.error}`);
    }
    
    await this.showMainMenu();
  }

  /**
   * Ask question helper
   */
  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer);
      });
    });
  }
}

// Start the CLI if run directly
if (require.main === module) {
  const cli = new EnhancedCodeGeneratorCLI();
  cli.start().catch(console.error);
}

module.exports = EnhancedCodeGeneratorCLI;
