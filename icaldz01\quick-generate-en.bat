@echo off
title Quick Activation Code Generator - iCalDZ

echo.
echo ========================================================================
echo                Quick Activation Code Generator - iCalDZ
echo ========================================================================
echo.

if "%~1"=="" (
    set /p client_name="Enter client name (or press Enter to skip): "
) else (
    set client_name=%~1
)

echo.
echo Generating activation code...
echo.

if "%client_name%"=="" (
    node generate-activation-code-en.js
) else (
    node generate-activation-code-en.js "%client_name%"
)

echo.
echo ========================================================================
echo Activation code generated successfully!
echo.
echo Tip: You can also use the command directly:
echo    quick-generate-en.bat "Client Name"
echo.
pause
