@echo off
echo ========================================
echo iCalDZ Accounting System - Clean Rebuild
echo ========================================

echo.
echo 1. Cleaning previous build...
if exist dist rmdir /s /q dist
if exist node_modules\.cache rmdir /s /q node_modules\.cache

echo.
echo 2. Installing dependencies...
call npm install

echo.
echo 3. Building application...
call npm run build

echo.
echo 4. Build completed!
echo.
echo To run with Electron:
echo   npm run electron
echo.
echo To run development server:
echo   npm run dev
echo.
pause
