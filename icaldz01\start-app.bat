@echo off
title نظام المحاسبي - iCode DZ
color 0A

echo.
echo ========================================
echo    نظام المحاسبي - iCode DZ
echo    تطوير: iCode DZ
echo    الهاتف: +213 551 93 05 89
echo ========================================
echo.

echo [1] تشغيل التطبيق في وضع التطوير
echo [2] بناء التطبيق للإنتاج
echo [3] تشغيل تطبيق Electron
echo [4] بناء ملف قابل للتشغيل
echo [5] تثبيت المكتبات
echo [0] خروج
echo.

set /p choice="اختر رقم العملية: "

if "%choice%"=="1" (
    echo.
    echo جاري تشغيل التطبيق...
    echo سيتم فتح المتصفح تلقائياً على http://localhost:3000
    echo.
    npm run dev
) else if "%choice%"=="2" (
    echo.
    echo جاري بناء التطبيق للإنتاج...
    npm run build
    echo.
    echo تم بناء التطبيق بنجاح!
    pause
) else if "%choice%"=="3" (
    echo.
    echo جاري تشغيل تطبيق Electron...
    npm run electron-dev
) else if "%choice%"=="4" (
    echo.
    echo جاري بناء ملف قابل للتشغيل...
    echo هذا قد يستغرق عدة دقائق...
    npm run dist
    echo.
    echo تم إنشاء الملف القابل للتشغيل في مجلد release
    echo يمكنك الآن توزيع الملف على أي جهاز
    pause
) else if "%choice%"=="5" (
    echo.
    echo جاري تثبيت المكتبات...
    npm install
    echo.
    echo تم تثبيت المكتبات بنجاح!
    pause
) else if "%choice%"=="0" (
    exit
) else (
    echo.
    echo خيار غير صحيح!
    pause
    goto start
)

:start
cls
goto :eof
