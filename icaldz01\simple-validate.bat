@echo off
title iCalDZ Code Validator

echo.
echo ========================================================================
echo                    iCalDZ Code Validator
echo ========================================================================
echo.

if "%~1"=="" (
    echo Enter activation code to validate:
    set /p activation_code=
) else (
    set activation_code=%~1
)

if "%activation_code%"=="" (
    echo ERROR: No activation code entered
    pause
    exit /b 1
)

echo.
echo Validating code...
echo.

node generate-activation-code-en.js --validate "%activation_code%"

echo.
echo ========================================================================
pause
