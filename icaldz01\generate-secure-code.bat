@echo off
title iCalDZ Secure Activation Code Generator

echo.
echo ========================================================
echo    iCalDZ Secure Activation Code Generator v2.0
echo    Machine-Specific Lifetime License System
echo ========================================================
echo.

:MENU
echo Choose an option:
echo.
echo 1. Generate Universal Code
echo 2. Generate Machine-Specific Code
echo 3. Generate Test Code (1 Week Trial)
echo 4. Validate Activation Code
echo 5. Show Help
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto UNIVERSAL
if "%choice%"=="2" goto MACHINE_SPECIFIC
if "%choice%"=="3" goto TEST_CODE
if "%choice%"=="4" goto VALIDATE
if "%choice%"=="5" goto HELP
if "%choice%"=="6" goto EXIT
echo Invalid choice. Please try again.
goto MENU

:UNIVERSAL
echo.
echo === Generate Universal Activation Code ===
echo.
set /p client_name="Enter client name (or press Enter for default): "
if "%client_name%"=="" set client_name=General Client

echo.
echo Generating secure activation code...
node generate-activation-code-en.js "%client_name%"
echo.
pause
goto MENU

:MACHINE_SPECIFIC
echo.
echo === Generate Machine-Specific Activation Code ===
echo.
set /p client_name="Enter client name: "
set /p machine_id="Enter machine ID (16 characters): "

if "%client_name%"=="" (
    echo Error: Client name is required for machine-specific codes.
    pause
    goto MENU
)

if "%machine_id%"=="" (
    echo Error: Machine ID is required for machine-specific codes.
    pause
    goto MENU
)

echo.
echo Generating secure machine-specific activation code...
node generate-activation-code-en.js "%client_name%" "%machine_id%"
echo.
pause
goto MENU

:TEST_CODE
echo.
echo === Generate Test Code (1 Week Trial - One Time Use) ===
echo.
echo WARNING: Test codes can only be used ONCE and expire after 1 week!
echo.
set /p client_name="Enter client name (or press Enter for default): "
if "%client_name%"=="" set client_name=Test Client

echo.
echo Generating 1-week trial activation code (single use only)...
node generate-activation-code-en.js "%client_name%" --trial
echo.
echo IMPORTANT: This test code can only be activated ONCE!
echo Save this code securely as it cannot be regenerated.
echo.
pause
goto MENU

:VALIDATE
echo.
echo === Validate Activation Code ===
echo.
set /p code="Enter activation code to validate: "
set /p machine_id="Enter machine ID (optional, press Enter to skip): "

if "%code%"=="" (
    echo Error: Activation code is required.
    pause
    goto MENU
)

echo.
echo Validating activation code...
if "%machine_id%"=="" (
    node generate-activation-code-en.js --validate "%code%"
) else (
    node generate-activation-code-en.js --validate "%code%" "%machine_id%"
)
echo.
pause
goto MENU

:HELP
echo.
echo === Help Information ===
echo.
echo This is a secure activation code generator with features:
echo.
echo - Cryptographic signatures prevent code forgery
echo - Machine fingerprinting for device binding
echo - One-time use tracking in secure database
echo - HMAC-SHA256 verification for authenticity
echo.
echo Universal codes work on any machine but tracked for one-time use.
echo Machine-specific codes only work on the specified machine ID.
echo Test codes provide 1-week trial access for evaluation purposes.
echo.
echo IMPORTANT: Test codes can only be activated ONCE per generation!
echo.
echo For technical support:
echo Phone: +213 551 93 05 89
echo Email: <EMAIL>
echo Website: www.icodedz.com
echo.
pause
goto MENU

:EXIT
echo.
echo Thank you for using iCalDZ Secure Activation Code Generator!
echo.
pause
exit
