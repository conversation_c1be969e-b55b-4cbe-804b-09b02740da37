@echo off
chcp 65001 >nul
title إنشاء كود تفعيل سريع - iCalDZ
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🚀 إنشاء كود تفعيل سريع - iCalDZ                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

if "%~1"=="" (
    set /p client_name="أدخل اسم العميل (أو اضغط Enter للتخطي): "
) else (
    set client_name=%~1
)

echo.
echo ⏳ جاري إنشاء كود التفعيل...
echo.

if "%client_name%"=="" (
    node generate-activation-code.js
) else (
    node generate-activation-code.js "%client_name%"
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ✅ تم إنشاء كود التفعيل بنجاح!
echo.
echo 💡 نصيحة: يمكنك أيضاً استخدام الأمر التالي مباشرة:
echo    quick-generate.bat "اسم العميل"
echo.
pause
