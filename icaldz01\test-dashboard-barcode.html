<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Barcode Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #28a745;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #218838;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Dashboard Barcode Scanning Test</h1>
        <p>This page tests the dashboard barcode scanning functionality to ensure it works correctly.</p>

        <div class="test-section">
            <h2>📷 Barcode Scanner Simulation</h2>
            <p>Enter a barcode and test the functionality:</p>
            <input type="text" id="barcodeInput" class="test-input" placeholder="Enter barcode (e.g., 000000001)" />
            <br>
            <button class="test-button" onclick="testBarcodeDisplay()">Test Display Product</button>
            <button class="test-button" onclick="testBarcodeOpenInvoice()">Test Open Invoice</button>
            <button class="test-button" onclick="clearTest()">Clear</button>
            <div id="testResult" class="test-result info">
                Ready to test barcode scanning...
            </div>
        </div>

        <div class="test-section">
            <h2>📦 Sample Products</h2>
            <p>Use these sample barcodes for testing:</p>
            <ul>
                <li><strong>000000001</strong> - شاي أحمر (Red Tea)</li>
                <li><strong>000000002</strong> - قهوة عربية (Arabic Coffee)</li>
                <li><strong>000000003</strong> - سكر أبيض (White Sugar)</li>
                <li><strong>000000004</strong> - زيت زيتون (Olive Oil)</li>
                <li><strong>000000005</strong> - أرز بسمتي (Basmati Rice)</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔍 Test Results</h2>
            <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 5px; min-height: 100px; font-family: monospace; font-size: 12px;">
                Test log will appear here...
            </div>
        </div>
    </div>

    <script>
        // Sample products data (matching the app's default products)
        const sampleProducts = [
            { id: 'P001', name: 'شاي أحمر', barcode: '000000001', sellPrice: 150, stock: 50 },
            { id: 'P002', name: 'قهوة عربية', barcode: '000000002', sellPrice: 300, stock: 30 },
            { id: 'P003', name: 'سكر أبيض', barcode: '000000003', sellPrice: 80, stock: 100 },
            { id: 'P004', name: 'زيت زيتون', barcode: '000000004', sellPrice: 500, stock: 25 },
            { id: 'P005', name: 'أرز بسمتي', barcode: '000000005', sellPrice: 200, stock: 75 }
        ];

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResult(message, type = 'info') {
            const resultElement = document.getElementById('testResult');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = message;
        }

        function findProductByBarcode(barcode) {
            return sampleProducts.find(p => p.barcode === barcode && barcode.trim() !== '');
        }

        function testBarcodeDisplay() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            log(`🔍 Testing barcode display for: ${barcode}`);
            
            if (barcode.length < 3) {
                showResult('❌ الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل', 'error');
                log('❌ Barcode too short');
                return;
            }

            const foundProduct = findProductByBarcode(barcode);
            if (foundProduct) {
                showResult(`✅ Product Found: ${foundProduct.name} - Price: ${foundProduct.sellPrice} - Stock: ${foundProduct.stock}`, 'success');
                log(`✅ Product found: ${foundProduct.name}`);
            } else {
                showResult(`❌ لم يتم العثور على المنتج بالباركود: ${barcode}`, 'error');
                log(`❌ Product not found for barcode: ${barcode}`);
            }
        }

        function testBarcodeOpenInvoice() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            log(`🛒 Testing open invoice for barcode: ${barcode}`);
            
            if (barcode.length < 3) {
                showResult('❌ الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل', 'error');
                log('❌ Barcode too short for invoice');
                return;
            }

            const foundProduct = findProductByBarcode(barcode);
            if (foundProduct) {
                if (foundProduct.stock <= 0) {
                    showResult(`❌ المنتج غير متوفر في المخزون: ${foundProduct.name}`, 'error');
                    log(`❌ Product out of stock: ${foundProduct.name}`);
                    return;
                }

                // Simulate creating invoice with product
                const newItem = {
                    productId: foundProduct.id,
                    productName: foundProduct.name,
                    quantity: 1,
                    price: foundProduct.sellPrice,
                    total: foundProduct.sellPrice
                };

                const invoice = {
                    invoiceNumber: 'INV-' + Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    customerId: 'GUEST',
                    customerName: 'زبون عابر',
                    paymentMethod: 'نقداً',
                    items: [newItem],
                    total: newItem.total,
                    discount: 0,
                    tax: newItem.total * 0.19, // 19% tax
                    finalTotal: newItem.total + (newItem.total * 0.19)
                };

                showResult(`🛒 Invoice Created Successfully! Product: ${foundProduct.name} added to invoice ${invoice.invoiceNumber}`, 'success');
                log(`🛒 Invoice created: ${invoice.invoiceNumber} with product: ${foundProduct.name}`);
                log(`💰 Total: ${invoice.finalTotal.toFixed(2)}`);
            } else {
                showResult(`❌ لم يتم العثور على المنتج بالباركود: ${barcode}`, 'error');
                log(`❌ Product not found for invoice: ${barcode}`);
            }
        }

        function clearTest() {
            document.getElementById('barcodeInput').value = '';
            showResult('Ready to test barcode scanning...', 'info');
            log('🧹 Test cleared');
        }

        // Initialize
        log('🧪 Dashboard Barcode Test initialized');
        log('📦 Sample products loaded: ' + sampleProducts.length);
    </script>
</body>
</html>
