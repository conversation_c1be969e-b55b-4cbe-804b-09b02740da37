#!/usr/bin/env node

/**
 * مولد أكواد التجربة لنظام iCalDZ
 * Trial Code Generator for iCalDZ System
 */

const CryptoJS = require('crypto-js');

class TrialCodeGenerator {
  constructor() {
    this.prefix = 'ICAL';
    this.year = 2025;
    this.secretKey = 'iCalDZ-2025-SecureKey-Advanced-Protection';
  }

  /**
   * توليد معرف فريد
   */
  generateUniqueId() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 14; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * تنسيق كود التفعيل
   */
  formatActivationCode(encrypted) {
    // أخذ أول 32 حرف من التشفير
    const code = encrypted.substring(0, 32);

    // تقسيم إلى مجموعات من 4 أحرف
    const groups = [];
    for (let i = 0; i < code.length; i += 4) {
      groups.push(code.substring(i, i + 4));
    }

    // تكوين الكود النهائي
    return `${this.prefix}-${this.year}-${groups.join('-')}`;
  }

  /**
   * توليد كود تجربة لمدة يوم واحد
   */
  generateOneDayTrial(clientName = 'Test User') {
    return this.generateTrialCode(clientName, 1);
  }

  /**
   * توليد كود تجربة لمدة 7 أيام
   */
  generateSevenDayTrial(clientName = 'Test User') {
    return this.generateTrialCode(clientName, 7);
  }

  /**
   * توليد كود تجربة مخصص
   */
  generateTrialCode(clientName, days) {
    try {
      const uniqueId = this.generateUniqueId();

      // حساب تاريخ انتهاء الصلاحية
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + days);

      // إنشاء البيانات
      const data = {
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        clientName: clientName,
        timestamp: Date.now(),
        type: 'TRIAL',
        trialDays: days,
        expiryDate: expiryDate.toISOString()
      };

      // تشفير البيانات
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey).toString();

      // تنسيق الكود
      const code = this.formatActivationCode(encrypted);

      return {
        activationCode: code,
        clientId: uniqueId,
        clientName: clientName,
        type: 'TRIAL',
        trialDays: days,
        expiryDate: expiryDate.toISOString(),
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('خطأ في توليد كود التجربة:', error);
      return null;
    }
  }

  /**
   * توليد كود تفعيل دائم
   */
  generateLifetimeCode(clientName = 'Licensed User') {
    try {
      const uniqueId = this.generateUniqueId();

      const data = {
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        clientName: clientName,
        timestamp: Date.now(),
        type: 'LIFETIME'
      };

      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey).toString();
      const code = this.formatActivationCode(encrypted);

      return {
        activationCode: code,
        clientId: uniqueId,
        clientName: clientName,
        type: 'LIFETIME',
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('خطأ في توليد كود التفعيل:', error);
      return null;
    }
  }
}

// تشغيل المولد
function main() {
  const generator = new TrialCodeGenerator();
  const args = process.argv.slice(2);

  console.log('🔐 مولد أكواد التجربة لنظام iCalDZ');
  console.log('=====================================');
  console.log('');

  if (args.length === 0) {
    // توليد أكواد افتراضية
    console.log('📅 توليد أكواد التجربة الافتراضية:');
    console.log('');

    // كود تجربة يوم واحد
    const oneDayCode = generator.generateOneDayTrial('Test User');
    if (oneDayCode) {
      console.log('🕐 كود تجربة يوم واحد:');
      console.log(`   الكود: ${oneDayCode.activationCode}`);
      console.log(`   ينتهي في: ${new Date(oneDayCode.expiryDate).toLocaleDateString('ar-DZ')}`);
      console.log('');
    }

    // كود تجربة 7 أيام
    const sevenDayCode = generator.generateSevenDayTrial('Test User');
    if (sevenDayCode) {
      console.log('📅 كود تجربة 7 أيام:');
      console.log(`   الكود: ${sevenDayCode.activationCode}`);
      console.log(`   ينتهي في: ${new Date(sevenDayCode.expiryDate).toLocaleDateString('ar-DZ')}`);
      console.log('');
    }

    // كود تفعيل دائم
    const lifetimeCode = generator.generateLifetimeCode('Licensed User');
    if (lifetimeCode) {
      console.log('♾️  كود تفعيل دائم:');
      console.log(`   الكود: ${lifetimeCode.activationCode}`);
      console.log('   صالح: مدى الحياة');
      console.log('');
    }

  } else {
    // معالجة المعاملات
    const command = args[0];
    const clientName = args[1] || 'Test User';

    switch (command) {
      case '1day':
      case 'oneday':
        const oneDayCode = generator.generateOneDayTrial(clientName);
        if (oneDayCode) {
          console.log('🕐 كود تجربة يوم واحد:');
          console.log(`الكود: ${oneDayCode.activationCode}`);
        }
        break;

      case '7days':
      case 'week':
        const sevenDayCode = generator.generateSevenDayTrial(clientName);
        if (sevenDayCode) {
          console.log('📅 كود تجربة 7 أيام:');
          console.log(`الكود: ${sevenDayCode.activationCode}`);
        }
        break;

      case 'lifetime':
      case 'permanent':
        const lifetimeCode = generator.generateLifetimeCode(clientName);
        if (lifetimeCode) {
          console.log('♾️  كود تفعيل دائم:');
          console.log(`الكود: ${lifetimeCode.activationCode}`);
        }
        break;

      case 'custom':
        const days = parseInt(args[2]) || 1;
        const customCode = generator.generateTrialCode(clientName, days);
        if (customCode) {
          console.log(`📅 كود تجربة ${days} أيام:`);
          console.log(`الكود: ${customCode.activationCode}`);
        }
        break;

      default:
        console.log('❌ أمر غير معروف. الأوامر المتاحة:');
        console.log('   1day [اسم العميل] - كود تجربة يوم واحد');
        console.log('   7days [اسم العميل] - كود تجربة 7 أيام');
        console.log('   lifetime [اسم العميل] - كود تفعيل دائم');
        console.log('   custom [اسم العميل] [عدد الأيام] - كود تجربة مخصص');
    }
  }

  console.log('');
  console.log('💡 نصائح:');
  console.log('   - انسخ الكود كاملاً مع الشرطات');
  console.log('   - كل كود يمكن استخدامه مرة واحدة فقط');
  console.log('   - أكواد التجربة تنتهي صلاحيتها تلقائياً');
}

// تشغيل البرنامج
if (require.main === module) {
  main();
}

module.exports = TrialCodeGenerator;
