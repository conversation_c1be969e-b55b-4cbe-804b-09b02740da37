import React, { useState, useEffect } from 'react';
import { useLanguage } from './LanguageContext.jsx';

const AdvancedReports = ({
  savedInvoices,
  products,
  expenses,
  formatPrice,
  showToast
}) => {
  const { t, currentLanguage } = useLanguage();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  const [reportType, setReportType] = useState('daily');
  const [reportData, setReportData] = useState(null);

  // Generate report based on selected type and date
  const generateReport = () => {
    if (reportType === 'daily') {
      generateDailyReport();
    } else if (reportType === 'monthly') {
      generateMonthlyReport();
    }
  };

  // Generate daily report with product details
  const generateDailyReport = () => {
    console.log('📊 Generating daily report for:', selectedDate);

    // Filter invoices for selected date
    const dailyInvoices = savedInvoices.filter(inv => {
      const invDate = new Date(inv.date).toISOString().split('T')[0];
      return invDate === selectedDate;
    });

    // Calculate total sales
    const totalSales = dailyInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);

    // Get all sold products with details
    const soldProducts = {};
    let totalCost = 0;

    dailyInvoices.forEach(invoice => {
      invoice.items.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          const productKey = item.productId;
          if (!soldProducts[productKey]) {
            soldProducts[productKey] = {
              id: product.id,
              name: product.name,
              category: product.category,
              buyPrice: product.buyPrice || 0,
              sellPrice: product.sellPrice || product.price || 0,
              totalQuantity: 0,
              totalRevenue: 0,
              totalCost: 0,
              profit: 0
            };
          }

          soldProducts[productKey].totalQuantity += item.quantity;
          soldProducts[productKey].totalRevenue += item.total;
          soldProducts[productKey].totalCost += (product.buyPrice || 0) * item.quantity;
          soldProducts[productKey].profit = soldProducts[productKey].totalRevenue - soldProducts[productKey].totalCost;

          totalCost += (product.buyPrice || 0) * item.quantity;
        }
      });
    });

    // Convert to array and sort by revenue
    const soldProductsArray = Object.values(soldProducts).sort((a, b) => b.totalRevenue - a.totalRevenue);

    // Calculate daily expenses
    const dailyExpenses = expenses.filter(exp => {
      const expDate = new Date(exp.date).toISOString().split('T')[0];
      return expDate === selectedDate;
    });
    const totalExpenses = dailyExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

    // Calculate profits
    const grossProfit = totalSales - totalCost;
    const netProfit = grossProfit - totalExpenses;
    const profitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0;

    setReportData({
      type: 'daily',
      date: selectedDate,
      totalSales,
      totalCost,
      totalExpenses,
      grossProfit,
      netProfit,
      profitMargin,
      invoicesCount: dailyInvoices.length,
      soldProducts: soldProductsArray,
      expenses: dailyExpenses
    });

    showToast(t('dailyReportGenerated', `📊 تم إنشاء التقرير اليومي لتاريخ ${new Date(selectedDate).toLocaleDateString(currentLanguage === 'ar' ? 'ar-DZ' : currentLanguage === 'fr' ? 'fr-FR' : 'en-US')}`), 'success', 3000);
  };

  // Generate monthly report
  const generateMonthlyReport = () => {
    console.log('📊 Generating monthly report for:', selectedMonth);

    const [year, month] = selectedMonth.split('-');

    // Filter invoices for selected month
    const monthlyInvoices = savedInvoices.filter(inv => {
      const invDate = new Date(inv.date);
      return invDate.getFullYear() === parseInt(year) && invDate.getMonth() === parseInt(month) - 1;
    });

    // Calculate total sales
    const totalSales = monthlyInvoices.reduce((sum, inv) => sum + (parseFloat(inv.finalTotal) || 0), 0);

    // Get all sold products with details
    const soldProducts = {};
    let totalCost = 0;

    monthlyInvoices.forEach(invoice => {
      invoice.items.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          const productKey = item.productId;
          if (!soldProducts[productKey]) {
            soldProducts[productKey] = {
              id: product.id,
              name: product.name,
              category: product.category,
              buyPrice: product.buyPrice || 0,
              sellPrice: product.sellPrice || product.price || 0,
              totalQuantity: 0,
              totalRevenue: 0,
              totalCost: 0,
              profit: 0
            };
          }

          soldProducts[productKey].totalQuantity += item.quantity;
          soldProducts[productKey].totalRevenue += item.total;
          soldProducts[productKey].totalCost += (product.buyPrice || 0) * item.quantity;
          soldProducts[productKey].profit = soldProducts[productKey].totalRevenue - soldProducts[productKey].totalCost;

          totalCost += (product.buyPrice || 0) * item.quantity;
        }
      });
    });

    // Convert to array and sort by revenue
    const soldProductsArray = Object.values(soldProducts).sort((a, b) => b.totalRevenue - a.totalRevenue);

    // Calculate monthly expenses
    const monthlyExpenses = expenses.filter(exp => {
      const expDate = new Date(exp.date);
      return expDate.getFullYear() === parseInt(year) && expDate.getMonth() === parseInt(month) - 1;
    });
    const totalExpenses = monthlyExpenses.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);

    // Calculate profits
    const grossProfit = totalSales - totalCost;
    const netProfit = grossProfit - totalExpenses;
    const profitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0;

    setReportData({
      type: 'monthly',
      date: selectedMonth,
      totalSales,
      totalCost,
      totalExpenses,
      grossProfit,
      netProfit,
      profitMargin,
      invoicesCount: monthlyInvoices.length,
      soldProducts: soldProductsArray,
      expenses: monthlyExpenses
    });

    // Get month name using translation system
    const monthKey = `month${parseInt(month)}`;
    const monthName = t(monthKey, ['جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان', 'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'][parseInt(month) - 1]);

    showToast(t('monthlyReportGenerated', `📊 تم إنشاء التقرير الشهري لشهر ${monthName} ${year}`), 'success', 3000);
  };

  // Print report
  const printReport = () => {
    if (!reportData) {
      showToast(t('generateReportFirst', '⚠️ يرجى إنشاء التقرير أولاً'), 'warning', 3000);
      return;
    }

    const reportTitle = reportData.type === 'daily'
      ? `${t('dailyReport', 'التقرير اليومي')} - ${new Date(reportData.date).toLocaleDateString(currentLanguage === 'ar' ? 'ar-DZ' : currentLanguage === 'fr' ? 'fr-FR' : 'en-US')}`
      : `${t('monthlyReport', 'التقرير الشهري')} - ${reportData.date}`;

    // Get language settings
    const isRTL = currentLanguage === 'ar';
    const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

    const printContent = `
      <!DOCTYPE html>
      <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${langCode}">
      <head>
        <meta charset="UTF-8">
        <title>${reportTitle}</title>
        <style>
          body { font-family: Arial, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }
          .summary-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
          .products-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
          .products-table th, .products-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          .products-table th { background-color: #f5f5f5; }
          .best-product { background-color: #e8f5e8; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportTitle}</h1>
          <p>${t('creationDate', 'تاريخ الإنشاء')}: ${new Date().toLocaleDateString(langCode === 'ar' ? 'ar-DZ' : langCode === 'fr' ? 'fr-FR' : 'en-US')}</p>
        </div>

        <div class="summary">
          <div class="summary-card">
            <h3>${t('totalSales', 'إجمالي المبيعات')}</h3>
            <p>${formatPrice(reportData.totalSales)}</p>
          </div>
          <div class="summary-card">
            <h3>${t('netProfit', 'صافي الربح')}</h3>
            <p>${formatPrice(reportData.netProfit)}</p>
          </div>
          <div class="summary-card">
            <h3>${t('profitMargin', 'هامش الربح')}</h3>
            <p>${reportData.profitMargin.toFixed(2)}%</p>
          </div>
        </div>

        <h2>${t('soldProductsDetails', 'تفاصيل المنتجات المباعة')}</h2>
        <table class="products-table">
          <thead>
            <tr>
              <th>${t('product', 'المنتج')}</th>
              <th>${t('category', 'الفئة')}</th>
              <th>${t('quantitySold', 'الكمية المباعة')}</th>
              <th>${t('totalRevenue', 'إجمالي الإيرادات')}</th>
              <th>${t('totalCost', 'إجمالي التكلفة')}</th>
              <th>${t('profit', 'الربح')}</th>
              <th>${t('profitMargin', 'هامش الربح')}</th>
            </tr>
          </thead>
          <tbody>
            ${reportData.soldProducts.map((product, index) => `
              <tr class="${index === 0 ? 'best-product' : ''}">
                <td>${product.name}</td>
                <td>${product.category}</td>
                <td>${product.totalQuantity}</td>
                <td>${formatPrice(product.totalRevenue)}</td>
                <td>${formatPrice(product.totalCost)}</td>
                <td>${formatPrice(product.profit)}</td>
                <td>${product.totalRevenue > 0 ? ((product.profit / product.totalRevenue) * 100).toFixed(2) : 0}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer" style="text-align: center; margin-top: 30px; color: #7f8c8d;">
          <p>${t('reportGeneratedBy', 'تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ')}</p>
          <p><strong>Developed by iCode DZ</strong></p>
          <p><strong>📞 0551930589</strong></p>
          <p>${t('allRightsReserved', '© 2025 iCode DZ - جميع الحقوق محفوظة')}</p>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank', 'width=1200,height=800');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.onload = () => setTimeout(() => printWindow.print(), 1000);

    showToast(t('reportOpenedForPrint', '🖨️ تم فتح التقرير للطباعة'), 'success', 3000);
  };

  // Auto-generate report when date changes
  useEffect(() => {
    if (savedInvoices.length > 0) {
      generateReport();
    }
  }, [selectedDate, selectedMonth, reportType]);

  return {
    selectedDate,
    setSelectedDate,
    selectedMonth,
    setSelectedMonth,
    reportType,
    setReportType,
    reportData,
    generateReport,
    printReport
  };
};

export default AdvancedReports;
