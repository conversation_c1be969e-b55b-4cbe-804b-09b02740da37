/**
 * Enhanced Security System Test Script
 * Demonstrates all security features beyond machine ID
 * 
 * <AUTHOR> DZ
 * @version 2.0.0
 */

const EnhancedActivationCodeGenerator = require('./generate-enhanced-activation-code');

class SecurityTester {
  constructor() {
    this.generator = new EnhancedActivationCodeGenerator();
    this.testResults = [];
  }

  /**
   * Run all security tests
   */
  async runAllTests() {
    console.log('🔐 Enhanced Security System Test Suite');
    console.log('━'.repeat(50));
    
    await this.testBasicSecurity();
    await this.testEnhancedSecurity();
    await this.testMaximumSecurity();
    await this.testTrialCodes();
    await this.testSecurityViolations();
    await this.testValidationScenarios();
    
    this.displayTestResults();
  }

  /**
   * Test basic security features
   */
  async testBasicSecurity() {
    console.log('\n🔒 Testing Basic Security...');
    
    try {
      // Generate basic security code
      const result = this.generator.generateEnhancedActivationCode({
        clientName: 'Test User Basic',
        machineId: 'TEST-MACHINE-001',
        securityLevel: 'BASIC',
        type: 'LIFETIME'
      });
      
      if (result) {
        console.log('✅ Basic code generation: PASSED');
        console.log(`   Code: ${result.activationCode}`);
        
        // Test validation
        const validation = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { machineId: 'TEST-MACHINE-001' }
        );
        
        if (validation.valid) {
          console.log('✅ Basic validation: PASSED');
        } else {
          console.log('❌ Basic validation: FAILED');
        }
        
        this.testResults.push({ test: 'Basic Security', status: 'PASSED' });
      } else {
        console.log('❌ Basic code generation: FAILED');
        this.testResults.push({ test: 'Basic Security', status: 'FAILED' });
      }
    } catch (error) {
      console.log('❌ Basic security test error:', error.message);
      this.testResults.push({ test: 'Basic Security', status: 'ERROR' });
    }
  }

  /**
   * Test enhanced security features
   */
  async testEnhancedSecurity() {
    console.log('\n🛡️ Testing Enhanced Security...');
    
    try {
      // Generate enhanced security code
      const result = this.generator.generateEnhancedActivationCode({
        clientName: 'Test User Enhanced',
        machineId: 'TEST-MACHINE-002',
        securityLevel: 'ENHANCED',
        type: 'LIFETIME',
        allowedIPs: ['*************', '***********'],
        maxDevices: 2,
        hardwareBinding: true,
        networkValidation: true
      });
      
      if (result) {
        console.log('✅ Enhanced code generation: PASSED');
        console.log(`   Code: ${result.activationCode}`);
        console.log(`   Security Features: ${Object.keys(result.securityFeatures).length}`);
        
        // Test validation with correct data
        const validationCorrect = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { 
            machineId: 'TEST-MACHINE-002',
            ipAddress: '*************',
            hardwareFingerprint: 'TEST-HARDWARE-FP-001'
          }
        );
        
        if (validationCorrect.valid) {
          console.log('✅ Enhanced validation (correct): PASSED');
        } else {
          console.log('❌ Enhanced validation (correct): FAILED');
        }
        
        // Test validation with wrong IP
        const validationWrongIP = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { 
            machineId: 'TEST-MACHINE-002',
            ipAddress: '********', // Wrong IP
            hardwareFingerprint: 'TEST-HARDWARE-FP-001'
          }
        );
        
        if (!validationWrongIP.valid) {
          console.log('✅ Enhanced validation (wrong IP): PASSED (correctly rejected)');
        } else {
          console.log('❌ Enhanced validation (wrong IP): FAILED (should reject)');
        }
        
        this.testResults.push({ test: 'Enhanced Security', status: 'PASSED' });
      } else {
        console.log('❌ Enhanced code generation: FAILED');
        this.testResults.push({ test: 'Enhanced Security', status: 'FAILED' });
      }
    } catch (error) {
      console.log('❌ Enhanced security test error:', error.message);
      this.testResults.push({ test: 'Enhanced Security', status: 'ERROR' });
    }
  }

  /**
   * Test maximum security features
   */
  async testMaximumSecurity() {
    console.log('\n🔐 Testing Maximum Security...');
    
    try {
      // Generate maximum security code
      const result = this.generator.generateEnhancedActivationCode({
        clientName: 'Test User Maximum',
        machineId: 'TEST-MACHINE-003',
        securityLevel: 'MAXIMUM',
        type: 'LIFETIME',
        allowedIPs: ['*************'],
        maxDevices: 1,
        geoRestriction: 'DZ',
        hardwareBinding: true,
        behaviorTracking: true,
        networkValidation: true,
        continuousMonitoring: true
      });
      
      if (result) {
        console.log('✅ Maximum code generation: PASSED');
        console.log(`   Code: ${result.activationCode}`);
        console.log(`   Security Level: ${result.securityLevel}`);
        console.log(`   All Features Enabled: ${JSON.stringify(result.securityFeatures, null, 2)}`);
        
        // Test validation
        const validation = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { 
            machineId: 'TEST-MACHINE-003',
            ipAddress: '*************',
            hardwareFingerprint: 'TEST-HARDWARE-FP-003',
            geolocation: 'DZ',
            behaviorData: { typing: 'normal', mouse: 'normal' }
          }
        );
        
        if (validation.valid) {
          console.log('✅ Maximum validation: PASSED');
        } else {
          console.log('❌ Maximum validation: FAILED');
        }
        
        this.testResults.push({ test: 'Maximum Security', status: 'PASSED' });
      } else {
        console.log('❌ Maximum code generation: FAILED');
        this.testResults.push({ test: 'Maximum Security', status: 'FAILED' });
      }
    } catch (error) {
      console.log('❌ Maximum security test error:', error.message);
      this.testResults.push({ test: 'Maximum Security', status: 'ERROR' });
    }
  }

  /**
   * Test trial codes
   */
  async testTrialCodes() {
    console.log('\n🧪 Testing Trial Codes...');
    
    try {
      // Generate 1-day trial code
      const trialResult = this.generator.generateEnhancedActivationCode({
        clientName: 'Trial User',
        securityLevel: 'ENHANCED',
        type: 'TRIAL',
        trialDays: 1
      });
      
      if (trialResult) {
        console.log('✅ Trial code generation: PASSED');
        console.log(`   Code: ${trialResult.activationCode}`);
        console.log(`   Expires: ${trialResult.expiryDate}`);
        
        // Test validation
        const validation = this.generator.validateEnhancedActivationCode(
          trialResult.activationCode,
          { timestamp: Date.now() }
        );
        
        if (validation.valid) {
          console.log('✅ Trial validation: PASSED');
        } else {
          console.log('❌ Trial validation: FAILED');
        }
        
        this.testResults.push({ test: 'Trial Codes', status: 'PASSED' });
      } else {
        console.log('❌ Trial code generation: FAILED');
        this.testResults.push({ test: 'Trial Codes', status: 'FAILED' });
      }
    } catch (error) {
      console.log('❌ Trial codes test error:', error.message);
      this.testResults.push({ test: 'Trial Codes', status: 'ERROR' });
    }
  }

  /**
   * Test security violations
   */
  async testSecurityViolations() {
    console.log('\n🚨 Testing Security Violations...');
    
    try {
      // Generate a code
      const result = this.generator.generateEnhancedActivationCode({
        clientName: 'Security Test User',
        machineId: 'TEST-MACHINE-004',
        securityLevel: 'ENHANCED',
        allowedIPs: ['*************']
      });
      
      if (result) {
        // Test wrong machine ID
        const wrongMachine = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { machineId: 'WRONG-MACHINE-ID' }
        );
        
        if (!wrongMachine.valid) {
          console.log('✅ Wrong machine ID rejection: PASSED');
        } else {
          console.log('❌ Wrong machine ID rejection: FAILED');
        }
        
        // Test wrong IP
        const wrongIP = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { 
            machineId: 'TEST-MACHINE-004',
            ipAddress: '********' // Not in allowed list
          }
        );
        
        if (!wrongIP.valid) {
          console.log('✅ Wrong IP rejection: PASSED');
        } else {
          console.log('❌ Wrong IP rejection: FAILED');
        }
        
        // Test invalid code format
        const invalidFormat = this.generator.validateEnhancedActivationCode(
          'INVALID-CODE-FORMAT',
          { machineId: 'TEST-MACHINE-004' }
        );
        
        if (!invalidFormat.valid) {
          console.log('✅ Invalid format rejection: PASSED');
        } else {
          console.log('❌ Invalid format rejection: FAILED');
        }
        
        this.testResults.push({ test: 'Security Violations', status: 'PASSED' });
      }
    } catch (error) {
      console.log('❌ Security violations test error:', error.message);
      this.testResults.push({ test: 'Security Violations', status: 'ERROR' });
    }
  }

  /**
   * Test various validation scenarios
   */
  async testValidationScenarios() {
    console.log('\n🔍 Testing Validation Scenarios...');
    
    try {
      // Test code reuse prevention
      const result = this.generator.generateEnhancedActivationCode({
        clientName: 'Reuse Test User',
        machineId: 'TEST-MACHINE-005',
        securityLevel: 'BASIC'
      });
      
      if (result) {
        // First validation (should work)
        const firstValidation = this.generator.validateEnhancedActivationCode(
          result.activationCode,
          { machineId: 'TEST-MACHINE-005' }
        );
        
        if (firstValidation.valid) {
          // Mark as used
          this.generator.markCodeAsUsed(result.activationCode, {
            machineId: 'TEST-MACHINE-005',
            timestamp: Date.now()
          });
          
          // Second validation (should fail)
          const secondValidation = this.generator.validateEnhancedActivationCode(
            result.activationCode,
            { machineId: 'TEST-MACHINE-005' }
          );
          
          if (!secondValidation.valid) {
            console.log('✅ Code reuse prevention: PASSED');
          } else {
            console.log('❌ Code reuse prevention: FAILED');
          }
        }
        
        this.testResults.push({ test: 'Validation Scenarios', status: 'PASSED' });
      }
    } catch (error) {
      console.log('❌ Validation scenarios test error:', error.message);
      this.testResults.push({ test: 'Validation Scenarios', status: 'ERROR' });
    }
  }

  /**
   * Display test results summary
   */
  displayTestResults() {
    console.log('\n📊 Test Results Summary');
    console.log('━'.repeat(50));
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`🚨 Errors: ${errors}`);
    console.log(`📈 Total: ${this.testResults.length}`);
    
    console.log('\nDetailed Results:');
    this.testResults.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' : 
                   result.status === 'FAILED' ? '❌' : '🚨';
      console.log(`${icon} ${result.test}: ${result.status}`);
    });
    
    if (passed === this.testResults.length) {
      console.log('\n🎉 All tests passed! Enhanced security system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the implementation.');
    }
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SecurityTester;
