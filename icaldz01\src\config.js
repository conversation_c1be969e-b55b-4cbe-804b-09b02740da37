/**
 * تكوين التطبيق الديناميكي
 * Dynamic Application Configuration
 */

class AppConfig {
  constructor() {
    this.defaultConfig = {
      // Server configuration
      servers: {
        defaultPorts: [3000, 3001, 3002, 3003, 5000, 5001, 8080, 8081],
        syncInterval: 30000, // 30 seconds
        requestTimeout: 5000, // 5 seconds
        retryAttempts: 3
      },
      
      // Profit calculation settings
      profit: {
        useDefaultPrices: false, // Don't use default prices
        defaultMargin: 0.3, // 30% margin (only for reference, not used)
        requireValidPrices: true, // Require valid buy/sell prices
        excludeInvalidProducts: true // Exclude products without valid prices
      },
      
      // Data validation
      validation: {
        strictMode: true, // Strict validation for calculations
        logWarnings: true, // Log validation warnings
        logErrors: true // Log validation errors
      },
      
      // UI settings
      ui: {
        showDetailedLogs: false, // Show detailed logs in UI
        autoRefreshReports: true, // Auto refresh reports
        refreshInterval: 60000 // 1 minute
      },
      
      // Build settings
      build: {
        environment: process.env.NODE_ENV || 'production',
        version: '1.0.0',
        buildDate: new Date().toISOString()
      }
    };
    
    this.config = this.loadConfig();
  }
  
  /**
   * Load configuration from localStorage or use defaults
   */
  loadConfig() {
    try {
      const savedConfig = localStorage.getItem('icaldz-config');
      if (savedConfig) {
        const parsed = JSON.parse(savedConfig);
        return this.mergeConfig(this.defaultConfig, parsed);
      }
    } catch (error) {
      console.warn('خطأ في تحميل التكوين، استخدام الإعدادات الافتراضية:', error);
    }
    
    return { ...this.defaultConfig };
  }
  
  /**
   * Save configuration to localStorage
   */
  saveConfig() {
    try {
      localStorage.setItem('icaldz-config', JSON.stringify(this.config));
      localStorage.setItem('icaldz-config-timestamp', Date.now().toString());
    } catch (error) {
      console.error('خطأ في حفظ التكوين:', error);
    }
  }
  
  /**
   * Merge configurations recursively
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
          merged[key] = this.mergeConfig(defaultConfig[key] || {}, userConfig[key]);
        } else {
          merged[key] = userConfig[key];
        }
      }
    }
    
    return merged;
  }
  
  /**
   * Get configuration value
   */
  get(path) {
    const keys = path.split('.');
    let value = this.config;
    
    for (const key of keys) {
      if (value && value.hasOwnProperty(key)) {
        value = value[key];
      } else {
        return undefined;
      }
    }
    
    return value;
  }
  
  /**
   * Set configuration value
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    this.saveConfig();
  }
  
  /**
   * Reset to default configuration
   */
  reset() {
    this.config = { ...this.defaultConfig };
    this.saveConfig();
  }
  
  /**
   * Get current server list based on current port
   */
  getServerList() {
    const currentUrl = new URL(window.location.origin);
    const currentPort = parseInt(currentUrl.port) || (currentUrl.protocol === 'https:' ? 443 : 80);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.hostname}`;
    
    const ports = this.get('servers.defaultPorts') || [3000, 3001, 3002];
    const servers = [];
    
    // Add current server first
    servers.push(window.location.origin);
    
    // Add alternative ports (excluding current)
    ports.forEach(port => {
      if (port !== currentPort) {
        servers.push(`${baseUrl}:${port}`);
      }
    });
    
    return [...new Set(servers)]; // Remove duplicates
  }
  
  /**
   * Validate profit calculation settings
   */
  validateProfitSettings() {
    const useDefault = this.get('profit.useDefaultPrices');
    const requireValid = this.get('profit.requireValidPrices');
    
    if (useDefault && requireValid) {
      console.warn('تحذير: تم تفعيل استخدام الأسعار الافتراضية والتحقق الصارم معاً');
    }
    
    return {
      useDefaultPrices: useDefault,
      requireValidPrices: requireValid,
      excludeInvalidProducts: this.get('profit.excludeInvalidProducts')
    };
  }
  
  /**
   * Get build information
   */
  getBuildInfo() {
    return {
      environment: this.get('build.environment'),
      version: this.get('build.version'),
      buildDate: this.get('build.buildDate'),
      isProduction: this.get('build.environment') === 'production',
      isDevelopment: this.get('build.environment') === 'development'
    };
  }
}

// Create global instance
export const appConfig = new AppConfig();

// Export for use in other modules
export default AppConfig;
