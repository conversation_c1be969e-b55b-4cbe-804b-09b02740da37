@echo off
chcp 65001 >nul
title التحقق من كود التفعيل - iCalDZ
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                      🔍 التحقق من كود التفعيل - iCalDZ                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

if "%~1"=="" (
    set /p activation_code="أدخل كود التفعيل للتحقق منه: "
) else (
    set activation_code=%~1
)

if "%activation_code%"=="" (
    echo ❌ لم يتم إدخال كود التفعيل
    echo.
    echo 💡 يمكنك استخدام الأمر التالي:
    echo    validate-code.bat "ICAL-2025-XXXX-XXXX-..."
    echo.
    pause
    exit /b 1
)

echo.
echo ⏳ جاري التحقق من كود التفعيل...
echo.

node generate-activation-code.js --validate "%activation_code%"

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
pause
