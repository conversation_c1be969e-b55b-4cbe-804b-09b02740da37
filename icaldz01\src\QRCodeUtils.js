import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';

/**
 * QR Code and Barcode Utilities for Repair Management
 * Handles QR code generation, scanning, and barcode creation
 */
class QRCodeUtils {
  constructor() {
    this.canvas = null;
  }

  /**
   * Generate QR Code for repair order
   * @param {Object} repairData - Repair order data
   * @returns {Promise<string>} Base64 QR code image
   */
  async generateRepairQRCode(repairData) {
    try {
      const qrData = {
        id: repairData.id,
        clientName: repairData.clientName,
        deviceName: repairData.deviceName,
        repairPrice: repairData.repairPrice,
        status: repairData.status,
        date: repairData.depositDate,
        type: 'repair'
      };

      const qrString = JSON.stringify(qrData);
      
      const qrCodeDataURL = await QRCode.toDataURL(qrString, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });

      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating QR code:', error);
      return null;
    }
  }

  /**
   * Generate small QR Code for table display
   * @param {Object} repairData - Repair order data
   * @returns {Promise<string>} Base64 QR code image (small size)
   */
  async generateSmallQRCode(repairData) {
    try {
      const qrData = {
        id: repairData.id,
        type: 'repair'
      };

      const qrString = JSON.stringify(qrData);
      
      const qrCodeDataURL = await QRCode.toDataURL(qrString, {
        width: 80,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'L'
      });

      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating small QR code:', error);
      return null;
    }
  }

  /**
   * Generate barcode for repair order
   * @param {string} repairId - Repair order ID
   * @returns {string} Base64 barcode image
   */
  generateRepairBarcode(repairId) {
    try {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, repairId, {
        format: "CODE128",
        width: 2,
        height: 50,
        displayValue: true,
        fontSize: 12,
        textMargin: 2
      });

      return canvas.toDataURL();
    } catch (error) {
      console.error('Error generating barcode:', error);
      return null;
    }
  }

  /**
   * Parse QR code data from scanned string
   * @param {string} qrString - Scanned QR code string
   * @returns {Object|null} Parsed repair data or null if invalid
   */
  parseQRCode(qrString) {
    try {
      const data = JSON.parse(qrString);
      
      if (data.type === 'repair' && data.id) {
        return data;
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing QR code:', error);
      return null;
    }
  }

  /**
   * Generate QR code for thermal printing (40x60mm)
   * @param {Object} repairData - Repair order data
   * @returns {Promise<string>} Base64 QR code optimized for thermal printing
   */
  async generateThermalQRCode(repairData) {
    try {
      const qrData = {
        id: repairData.id,
        client: repairData.clientName,
        device: repairData.deviceName,
        price: repairData.repairPrice + (repairData.partsPrice || 0),
        status: repairData.status,
        date: repairData.depositDate,
        type: 'repair_thermal'
      };

      const qrString = JSON.stringify(qrData);
      
      const qrCodeDataURL = await QRCode.toDataURL(qrString, {
        width: 120,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'H' // High error correction for thermal printing
      });

      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating thermal QR code:', error);
      return null;
    }
  }

  /**
   * Create QR code scanner interface
   * @param {Function} onScan - Callback function when QR code is scanned
   * @param {Function} onError - Callback function for errors
   */
  initQRScanner(onScan, onError) {
    // This would integrate with camera API for QR scanning
    // For now, we'll provide a text input fallback
    return {
      start: () => {
        console.log('QR Scanner started');
      },
      stop: () => {
        console.log('QR Scanner stopped');
      },
      parseManualInput: (input) => {
        const parsed = this.parseQRCode(input);
        if (parsed) {
          onScan(parsed);
        } else {
          onError('Invalid QR code format');
        }
      }
    };
  }

  /**
   * Generate repair ticket with QR code for printing
   * @param {Object} repairData - Repair order data
   * @param {Object} options - Printing options (language, format, etc.)
   * @returns {Promise<Object>} Print-ready data with QR code
   */
  async generatePrintableTicket(repairData, options = {}) {
    try {
      const qrCode = await this.generateThermalQRCode(repairData);
      const barcode = this.generateRepairBarcode(repairData.id);

      return {
        qrCode,
        barcode,
        repairData,
        printFormat: options.format || 'thermal',
        language: options.language || 'ar',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating printable ticket:', error);
      return null;
    }
  }
}

// Export singleton instance
export default new QRCodeUtils();
