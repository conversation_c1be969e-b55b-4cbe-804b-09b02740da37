/**
 * 🔧 Long-Term Stability Monitor for Barcode Scanner Fix
 * Monitors the application for extended periods to ensure barcode scanning remains functional
 */

class LongTermStabilityMonitor {
  constructor() {
    this.startTime = Date.now();
    this.barcodeTestCount = 0;
    this.shortcutTestCount = 0;
    this.errorCount = 0;
    this.lastBarcodeTest = null;
    this.lastShortcutTest = null;
    this.isRunning = false;
    
    // Test intervals (in milliseconds)
    this.BARCODE_TEST_INTERVAL = 30 * 60 * 1000; // 30 minutes
    this.SHORTCUT_TEST_INTERVAL = 15 * 60 * 1000; // 15 minutes
    this.STATUS_LOG_INTERVAL = 60 * 60 * 1000;    // 1 hour
    
    this.log('🔧 Long-Term Stability Monitor initialized');
  }

  /**
   * Start monitoring
   */
  start() {
    if (this.isRunning) {
      this.log('⚠️ Monitor already running');
      return;
    }

    this.isRunning = true;
    this.log('🚀 Starting long-term stability monitoring...');
    
    // Start periodic tests
    this.barcodeTestTimer = setInterval(() => this.testBarcodeScanning(), this.BARCODE_TEST_INTERVAL);
    this.shortcutTestTimer = setInterval(() => this.testKeyboardShortcuts(), this.SHORTCUT_TEST_INTERVAL);
    this.statusLogTimer = setInterval(() => this.logStatus(), this.STATUS_LOG_INTERVAL);
    
    // Initial tests
    setTimeout(() => this.testBarcodeScanning(), 5000);
    setTimeout(() => this.testKeyboardShortcuts(), 10000);
    
    this.log('✅ Long-term monitoring started successfully');
  }

  /**
   * Stop monitoring
   */
  stop() {
    if (!this.isRunning) {
      this.log('⚠️ Monitor not running');
      return;
    }

    this.isRunning = false;
    
    clearInterval(this.barcodeTestTimer);
    clearInterval(this.shortcutTestTimer);
    clearInterval(this.statusLogTimer);
    
    this.log('🛑 Long-term monitoring stopped');
    this.generateFinalReport();
  }

  /**
   * Test barcode scanning functionality
   */
  testBarcodeScanning() {
    try {
      this.log('🧪 Testing barcode scanning functionality...');
      
      // Simulate barcode input in different fields
      const testFields = [
        { id: 'dashboard-scanner', name: 'Dashboard Scanner' },
        { class: 'sales-scanner', name: 'Sales Scanner' },
        { id: 'product-barcode', name: 'Product Barcode' }
      ];

      let successCount = 0;
      
      testFields.forEach(field => {
        const element = field.id ? 
          document.getElementById(field.id) : 
          document.querySelector(`.${field.class}`);
          
        if (element) {
          // Simulate barcode input
          const testBarcode = `TEST${Date.now()}`;
          element.value = testBarcode;
          
          // Create and dispatch keyboard event
          const event = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            bubbles: true
          });
          
          // Test if KeyboardShortcuts interferes
          const beforeValue = element.value;
          element.dispatchEvent(event);
          const afterValue = element.value;
          
          if (beforeValue === afterValue) {
            successCount++;
            this.log(`✅ ${field.name}: Barcode input protected successfully`);
          } else {
            this.log(`❌ ${field.name}: Barcode input was interfered with`);
            this.errorCount++;
          }
          
          // Clear test value
          element.value = '';
        } else {
          this.log(`⚠️ ${field.name}: Field not found (may not be visible)`);
        }
      });

      this.barcodeTestCount++;
      this.lastBarcodeTest = {
        timestamp: new Date().toISOString(),
        success: successCount === testFields.length,
        details: `${successCount}/${testFields.length} tests passed`
      };
      
      this.log(`📊 Barcode test completed: ${this.lastBarcodeTest.details}`);
      
    } catch (error) {
      this.log(`❌ Barcode test error: ${error.message}`);
      this.errorCount++;
    }
  }

  /**
   * Test keyboard shortcuts functionality
   */
  testKeyboardShortcuts() {
    try {
      this.log('🧪 Testing keyboard shortcuts functionality...');
      
      // Test if KeyboardShortcuts is still responsive
      if (typeof KeyboardShortcuts !== 'undefined') {
        const status = KeyboardShortcuts.getStatus();
        
        const isHealthy = 
          status.isEnabled && 
          status.barcodeProtectionEnabled !== undefined &&
          status.activeWindow;
          
        if (isHealthy) {
          this.log('✅ Keyboard shortcuts system is healthy');
        } else {
          this.log('❌ Keyboard shortcuts system shows issues');
          this.errorCount++;
        }
        
        this.lastShortcutTest = {
          timestamp: new Date().toISOString(),
          success: isHealthy,
          status: status
        };
      } else {
        this.log('⚠️ KeyboardShortcuts not available for testing');
      }
      
      this.shortcutTestCount++;
      
    } catch (error) {
      this.log(`❌ Shortcut test error: ${error.message}`);
      this.errorCount++;
    }
  }

  /**
   * Log current status
   */
  logStatus() {
    const runtime = this.getRuntime();
    const memoryUsage = this.getMemoryUsage();
    
    this.log(`📊 === STABILITY STATUS REPORT ===`);
    this.log(`⏱️ Runtime: ${runtime}`);
    this.log(`🧪 Barcode Tests: ${this.barcodeTestCount} (Errors: ${this.errorCount})`);
    this.log(`⌨️ Shortcut Tests: ${this.shortcutTestCount}`);
    this.log(`💾 Memory Usage: ${memoryUsage}`);
    this.log(`🔧 Last Barcode Test: ${this.lastBarcodeTest ? this.lastBarcodeTest.details : 'None'}`);
    this.log(`⌨️ Last Shortcut Test: ${this.lastShortcutTest ? (this.lastShortcutTest.success ? 'Passed' : 'Failed') : 'None'}`);
    this.log(`=================================`);
  }

  /**
   * Get formatted runtime
   */
  getRuntime() {
    const runtime = Date.now() - this.startTime;
    const days = Math.floor(runtime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((runtime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((runtime % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${days}d ${hours}h ${minutes}m`;
  }

  /**
   * Get memory usage information
   */
  getMemoryUsage() {
    if (performance.memory) {
      const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
      return `${used}MB / ${total}MB`;
    }
    return 'Not available';
  }

  /**
   * Generate final report
   */
  generateFinalReport() {
    const runtime = this.getRuntime();
    const successRate = this.barcodeTestCount > 0 ? 
      ((this.barcodeTestCount - this.errorCount) / this.barcodeTestCount * 100).toFixed(2) : 0;
    
    this.log(`📋 === FINAL STABILITY REPORT ===`);
    this.log(`⏱️ Total Runtime: ${runtime}`);
    this.log(`🧪 Total Barcode Tests: ${this.barcodeTestCount}`);
    this.log(`⌨️ Total Shortcut Tests: ${this.shortcutTestCount}`);
    this.log(`❌ Total Errors: ${this.errorCount}`);
    this.log(`📈 Success Rate: ${successRate}%`);
    this.log(`🎯 Status: ${this.errorCount === 0 ? '✅ STABLE' : '⚠️ ISSUES DETECTED'}`);
    this.log(`================================`);
  }

  /**
   * Log with timestamp
   */
  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    
    // Also log to a dedicated element if available
    const logElement = document.getElementById('stabilityLog');
    if (logElement) {
      logElement.innerHTML += logMessage + '\n';
      logElement.scrollTop = logElement.scrollHeight;
    }
  }
}

// Global instance
window.StabilityMonitor = new LongTermStabilityMonitor();

// Auto-start monitoring when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => window.StabilityMonitor.start(), 1000);
  });
} else {
  setTimeout(() => window.StabilityMonitor.start(), 1000);
}

// Export for manual control
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LongTermStabilityMonitor;
}
