/**
 * Enhanced Activation Code Generator with Multiple Security Layers
 * Beyond Machine ID - Advanced Protection System
 *
 * <AUTHOR> DZ
 * @version 2.0.0
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class EnhancedActivationCodeGenerator {
  constructor() {
    this.prefix = 'ICAL';
    this.year = 2025;
    this.secretKey = 'iCalDZ-2025-Enhanced-Security-Key-v2.0';
    this.serverKey = 'iCalDZ-Server-Validation-Key-2025';
    this.usedCodesFile = path.join(__dirname, 'used-codes-enhanced.json');
    this.clientDatabase = path.join(__dirname, 'client-database.json');

    // Security levels
    this.securityLevels = {
      BASIC: 1,
      ENHANCED: 2,
      MAXIMUM: 3
    };

    // Initialize databases
    this.initializeDatabases();
  }

  /**
   * Initialize secure databases
   */
  initializeDatabases() {
    if (!fs.existsSync(this.usedCodesFile)) {
      fs.writeFileSync(this.usedCodesFile, JSON.stringify({}));
    }

    if (!fs.existsSync(this.clientDatabase)) {
      fs.writeFileSync(this.clientDatabase, JSON.stringify({}));
    }
  }

  /**
   * Generate Enhanced Activation Code with Multiple Security Layers
   */
  generateEnhancedActivationCode(options = {}) {
    try {
      const {
        clientName = 'Licensed User',
        machineId = null,
        securityLevel = 'ENHANCED',
        type = 'LIFETIME',
        trialDays = null,
        allowedIPs = [],
        maxDevices = 1,
        geoRestriction = null,
        hardwareBinding = true
      } = options;

      const uniqueId = this.generateUniqueId();
      const timestamp = Date.now();

      // Calculate expiry for trial codes
      let expiryDate = null;
      if (type === 'TRIAL' && trialDays) {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + trialDays);
      }

      // Enhanced security data
      const securityData = {
        // Basic information
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        clientName,
        timestamp,
        type,
        trialDays,
        expiryDate: expiryDate ? expiryDate.toISOString() : null,

        // Enhanced security features
        securityLevel: this.securityLevels[securityLevel] || this.securityLevels.ENHANCED,
        machineId,
        allowedIPs,
        maxDevices,
        geoRestriction,
        hardwareBinding,

        // Anti-tampering measures
        codeVersion: '2.0',
        securityHash: this.generateSecurityHash(uniqueId, timestamp),
        serverValidation: true,

        // Time-based security
        generationTime: timestamp,
        validationWindow: 30 * 24 * 60 * 60 * 1000, // 30 days for first activation

        // Additional security layers
        behaviorTracking: securityLevel === 'MAXIMUM',
        networkValidation: securityLevel !== 'BASIC',
        continuousMonitoring: securityLevel === 'MAXIMUM'
      };

      // Multi-layer encryption
      const encryptedData = this.multiLayerEncryption(securityData);
      const activationCode = this.formatEnhancedActivationCode(encryptedData);

      // Generate server validation token
      const serverToken = this.generateServerValidationToken(securityData);

      // Store in secure database
      this.storeSecureCodeData(activationCode, securityData, serverToken);

      return {
        activationCode,
        clientId: uniqueId,
        clientName,
        type,
        securityLevel,
        generatedAt: new Date().toISOString(),
        expiryDate: expiryDate ? expiryDate.toISOString() : null,
        serverToken, // For server-side validation
        securityFeatures: {
          machineBinding: !!machineId,
          ipRestriction: allowedIPs.length > 0,
          geoRestriction: !!geoRestriction,
          hardwareBinding,
          maxDevices,
          behaviorTracking: securityData.behaviorTracking,
          networkValidation: securityData.networkValidation,
          continuousMonitoring: securityData.continuousMonitoring
        }
      };
    } catch (error) {
      console.error('Error generating enhanced activation code:', error);
      return null;
    }
  }

  /**
   * Multi-layer encryption for enhanced security (Node.js compatible)
   */
  multiLayerEncryption(data) {
    try {
      // Layer 1: Hash-based encryption (fully compatible)
      const dataString = JSON.stringify(data);
      const hash = crypto.createHash('sha256').update(dataString + this.secretKey).digest('hex');

      // Layer 2: HMAC signature for integrity
      const hmac = crypto.createHmac('sha256', this.serverKey);
      hmac.update(hash);
      const signature = hmac.digest('hex');

      // Layer 3: Combine with metadata
      const finalData = {
        hash,
        signature,
        timestamp: Date.now(),
        version: '2.0'
      };

      return Buffer.from(JSON.stringify(finalData)).toString('base64');
    } catch (error) {
      console.error('Multi-layer encryption error:', error);
      // Ultimate fallback - simple encoding
      const simpleData = {
        data: Buffer.from(JSON.stringify(data)).toString('base64'),
        timestamp: Date.now(),
        version: '2.0-simple'
      };
      return Buffer.from(JSON.stringify(simpleData)).toString('base64');
    }
  }

  /**
   * Format enhanced activation code
   */
  formatEnhancedActivationCode(encryptedData) {
    // Create a hash of the encrypted data for the code
    const hash = crypto.createHash('sha256').update(encryptedData).digest('hex');

    // Take first 32 characters and format
    const code = hash.substring(0, 32).toUpperCase();
    const groups = code.match(/.{1,4}/g) || [];

    return `${this.prefix}-${this.year}-${groups.join('-')}`;
  }

  /**
   * Generate server validation token
   */
  generateServerValidationToken(data) {
    const tokenData = {
      clientId: data.clientId,
      timestamp: data.timestamp,
      securityLevel: data.securityLevel,
      type: data.type
    };

    const hmac = crypto.createHmac('sha256', this.serverKey);
    hmac.update(JSON.stringify(tokenData));
    return hmac.digest('hex');
  }

  /**
   * Generate security hash for anti-tampering
   */
  generateSecurityHash(clientId, timestamp) {
    const data = `${clientId}-${timestamp}-${this.secretKey}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }

  /**
   * Store secure code data
   */
  storeSecureCodeData(activationCode, securityData, serverToken) {
    try {
      // Store in used codes database
      const usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      usedCodes[activationCode] = {
        ...securityData,
        serverToken,
        used: false,
        generatedAt: new Date().toISOString()
      };
      fs.writeFileSync(this.usedCodesFile, JSON.stringify(usedCodes, null, 2));

      // Store in client database
      const clientDb = JSON.parse(fs.readFileSync(this.clientDatabase, 'utf8'));
      clientDb[securityData.clientId] = {
        clientName: securityData.clientName,
        activationCode,
        securityLevel: securityData.securityLevel,
        type: securityData.type,
        machineId: securityData.machineId,
        allowedIPs: securityData.allowedIPs,
        maxDevices: securityData.maxDevices,
        createdAt: new Date().toISOString()
      };
      fs.writeFileSync(this.clientDatabase, JSON.stringify(clientDb, null, 2));

      return true;
    } catch (error) {
      console.error('Error storing secure code data:', error);
      return false;
    }
  }

  /**
   * Validate enhanced activation code
   */
  validateEnhancedActivationCode(activationCode, validationData = {}) {
    try {
      const {
        machineId,
        hardwareFingerprint,
        ipAddress,
        geolocation,
        behaviorData
      } = validationData;

      // Check format
      if (!activationCode.startsWith(`${this.prefix}-${this.year}-`)) {
        return { valid: false, error: 'Invalid code format' };
      }

      // Load from database
      const usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      const codeData = usedCodes[activationCode];

      if (!codeData) {
        return { valid: false, error: 'Code not found or invalid' };
      }

      if (codeData.used) {
        return { valid: false, error: 'Code already used' };
      }

      // Validate expiry for trial codes
      if (codeData.type === 'TRIAL' && codeData.expiryDate) {
        if (new Date() > new Date(codeData.expiryDate)) {
          return { valid: false, error: 'Trial code expired' };
        }
      }

      // Enhanced security validations
      const securityChecks = this.performSecurityChecks(codeData, validationData);
      if (!securityChecks.passed) {
        return { valid: false, error: securityChecks.error };
      }

      return {
        valid: true,
        data: codeData,
        securityLevel: codeData.securityLevel,
        message: 'Enhanced activation code validated successfully'
      };
    } catch (error) {
      console.error('Enhanced validation error:', error);
      return { valid: false, error: 'Validation error occurred' };
    }
  }

  /**
   * Perform enhanced security checks
   */
  performSecurityChecks(codeData, validationData) {
    try {
      // Machine ID check
      if (codeData.machineId && validationData.machineId) {
        if (codeData.machineId !== validationData.machineId) {
          return { passed: false, error: 'Machine ID mismatch' };
        }
      }

      // IP address restriction
      if (codeData.allowedIPs && codeData.allowedIPs.length > 0) {
        if (!codeData.allowedIPs.includes(validationData.ipAddress)) {
          return { passed: false, error: 'IP address not allowed' };
        }
      }

      // Hardware fingerprint check
      if (codeData.hardwareBinding && validationData.hardwareFingerprint) {
        // Store hardware fingerprint for future validation
        if (!codeData.storedHardwareFingerprint) {
          codeData.storedHardwareFingerprint = validationData.hardwareFingerprint;
        } else if (codeData.storedHardwareFingerprint !== validationData.hardwareFingerprint) {
          return { passed: false, error: 'Hardware fingerprint mismatch' };
        }
      }

      // Geolocation restriction
      if (codeData.geoRestriction && validationData.geolocation) {
        // Implement geolocation validation logic here
        // This is a placeholder for geo-restriction checks
      }

      return { passed: true };
    } catch (error) {
      return { passed: false, error: 'Security check failed' };
    }
  }

  /**
   * Generate unique ID
   */
  generateUniqueId() {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}${random}`.toUpperCase();
  }

  /**
   * Mark code as used
   */
  markCodeAsUsed(activationCode, deviceInfo = {}) {
    try {
      const usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      if (usedCodes[activationCode]) {
        usedCodes[activationCode].used = true;
        usedCodes[activationCode].usedAt = new Date().toISOString();
        usedCodes[activationCode].deviceInfo = deviceInfo;
        fs.writeFileSync(this.usedCodesFile, JSON.stringify(usedCodes, null, 2));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking code as used:', error);
      return false;
    }
  }
}

module.exports = EnhancedActivationCodeGenerator;
