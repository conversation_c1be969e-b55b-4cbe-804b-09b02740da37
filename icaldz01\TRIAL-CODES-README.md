# 🔐 مولد أكواد التجربة لنظام iCalDZ
# Trial Code Generator for iCalDZ System

## 📋 نظرة عامة / Overview

هذا المولد يسمح بإنشاء أكواد تجربة مؤقتة وأكواد تفعيل دائمة لنظام iCalDZ.

This generator allows creating temporary trial codes and permanent activation codes for the iCalDZ system.

## 🚀 كيفية الاستخدام / How to Use

### 1. تشغيل المولد الأساسي / Basic Generator
```bash
node generate-trial-codes.js
```
سيقوم بتوليد:
- كود تجربة يوم واحد
- كود تجربة 7 أيام  
- كود تفعيل دائم

### 2. توليد كود تجربة يوم واحد / Generate 1-Day Trial
```bash
node generate-trial-codes.js 1day "اسم العميل"
node generate-trial-codes.js oneday "Client Name"
```

### 3. توليد كود تجربة 7 أيام / Generate 7-Day Trial
```bash
node generate-trial-codes.js 7days "اسم العميل"
node generate-trial-codes.js week "Client Name"
```

### 4. توليد كود تفعيل دائم / Generate Lifetime Code
```bash
node generate-trial-codes.js lifetime "اسم العميل"
node generate-trial-codes.js permanent "Client Name"
```

### 5. توليد كود تجربة مخصص / Generate Custom Trial
```bash
node generate-trial-codes.js custom "اسم العميل" 30
# سيولد كود تجربة لمدة 30 يوم
```

## 📝 أمثلة / Examples

### مثال 1: كود تجربة سريع
```bash
node generate-trial-codes.js 1day "زكا"
```
**النتيجة:**
```
🕐 كود تجربة يوم واحد:
الكود: ICAL-2025-A1B2-C3D4-E5F6-G7H8-I9J0-K1L2-M3N4-O5P6
```

### مثال 2: كود تجربة أسبوعي
```bash
node generate-trial-codes.js 7days "شركة الاختبار"
```
**النتيجة:**
```
📅 كود تجربة 7 أيام:
الكود: ICAL-2025-Q7R8-S9T0-U1V2-W3X4-Y5Z6-A7B8-C9D0-E1F2
```

## 🔧 الميزات / Features

### ✅ أنواع الأكواد المدعومة / Supported Code Types
- **كود تجربة يوم واحد** - للاختبار السريع
- **كود تجربة 7 أيام** - للتقييم المتوسط
- **كود تجربة مخصص** - أي عدد من الأيام
- **كود تفعيل دائم** - بدون انتهاء صلاحية

### 🔒 الأمان / Security
- تشفير AES-256 للبيانات
- معرف فريد لكل كود
- ربط بتاريخ انتهاء الصلاحية
- حماية من إعادة الاستخدام

### ⏰ إدارة الوقت / Time Management
- حساب تلقائي لتاريخ انتهاء الصلاحية
- عرض الأيام المتبقية في التطبيق
- إزالة تلقائية للأكواد المنتهية الصلاحية

## 🎯 كيفية استخدام الأكواد في التطبيق / How to Use Codes in App

1. **افتح التطبيق** / Open the application
2. **أدخل كود التفعيل** / Enter activation code
3. **اضغط تفعيل** / Click activate
4. **ستظهر رسالة نجاح** / Success message will appear

### للأكواد التجريبية / For Trial Codes:
- سيظهر عدد الأيام المتبقية في الواجهة
- تنبيه يومي بالأيام المتبقية
- إزالة تلقائية عند انتهاء الصلاحية

## 🛠️ متطلبات التشغيل / Requirements

```bash
npm install crypto-js
```

## 📞 الدعم / Support

إذا واجهت أي مشاكل:
1. تأكد من تثبيت crypto-js
2. تحقق من صحة تنسيق الكود
3. تأكد من عدم انتهاء صلاحية الكود

---

## 🔥 أكواد جاهزة للاختبار / Ready Test Codes

### كود تجربة يوم واحد (مثال):
```
ICAL-2025-A1B2-C3D4-E5F6-G7H8-I9J0-K1L2-M3N4-O5P6
```

### كود تجربة 7 أيام (مثال):
```
ICAL-2025-Q7R8-S9T0-U1V2-W3X4-Y5Z6-A7B8-C9D0-E1F2
```

**ملاحظة:** هذه أكواد مثال فقط. استخدم المولد لإنشاء أكواد حقيقية.

---

**© 2025 iCalDZ System - جميع الحقوق محفوظة**
