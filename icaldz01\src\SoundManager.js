/**
 * 🔊 Sound Manager System for iCalDZ Accounting System
 * Provides audio feedback for all user operations
 * 
 * Features:
 * - Different sounds for each operation type
 * - Volume control and mute functionality
 * - Fallback to system beep if audio files not available
 * - Context-aware sound selection
 */

class SoundManagerClass {
  constructor() {
    this.sounds = {};
    this.isEnabled = true;
    this.volume = 0.7;
    this.isInitialized = false;
    
    // Sound definitions with fallback system beep
    this.soundDefinitions = {
      // Sales operations
      newInvoice: {
        file: '/sounds/new-invoice.mp3',
        description: '🛒 فاتورة جديدة',
        fallback: 'beep'
      },
      saveInvoice: {
        file: '/sounds/save-invoice.mp3',
        description: '💾 حفظ الفاتورة',
        fallback: 'beep'
      },
      printInvoice: {
        file: '/sounds/print.mp3',
        description: '🖨️ طباعة الفاتورة',
        fallback: 'beep'
      },
      closeWindow: {
        file: '/sounds/close.mp3',
        description: '❌ إغلاق النافذة',
        fallback: 'click'
      },
      
      // General operations
      success: {
        file: '/sounds/success.mp3',
        description: '✅ نجاح العملية',
        fallback: 'beep'
      },
      warning: {
        file: '/sounds/warning.mp3',
        description: '⚠️ تحذير',
        fallback: 'beep'
      },
      error: {
        file: '/sounds/error.mp3',
        description: '❌ خطأ',
        fallback: 'beep'
      },
      
      // Product operations
      addProduct: {
        file: '/sounds/add-product.mp3',
        description: '➕ إضافة منتج',
        fallback: 'click'
      },
      deleteProduct: {
        file: '/sounds/delete.mp3',
        description: '🗑️ حذف منتج',
        fallback: 'click'
      },
      
      // Navigation and search
      quickSearch: {
        file: '/sounds/search.mp3',
        description: '🔍 البحث السريع',
        fallback: 'click'
      },
      refresh: {
        file: '/sounds/refresh.mp3',
        description: '🔄 تحديث البيانات',
        fallback: 'click'
      },
      
      // Keyboard shortcuts
      shortcutActivated: {
        file: '/sounds/shortcut.mp3',
        description: '⌨️ اختصار لوحة المفاتيح',
        fallback: 'click'
      }
    };
    
    this.init();
  }
  
  /**
   * Initialize the sound system
   */
  async init() {
    try {
      // Load user preferences
      this.loadPreferences();
      
      // Pre-load critical sounds
      await this.preloadSounds(['success', 'error', 'newInvoice', 'saveInvoice']);
      
      this.isInitialized = true;
      console.log('🔊 Sound Manager initialized successfully');
    } catch (error) {
      console.warn('🔇 Sound Manager initialization failed, using fallback mode:', error);
      this.isInitialized = true; // Still allow fallback sounds
    }
  }
  
  /**
   * Pre-load specific sounds for better performance
   */
  async preloadSounds(soundKeys) {
    const loadPromises = soundKeys.map(key => this.loadSound(key));
    await Promise.allSettled(loadPromises);
  }
  
  /**
   * Load a single sound file
   */
  async loadSound(soundKey) {
    if (this.sounds[soundKey]) return this.sounds[soundKey];
    
    const soundDef = this.soundDefinitions[soundKey];
    if (!soundDef) {
      console.warn(`🔇 Sound definition not found: ${soundKey}`);
      return null;
    }
    
    try {
      const audio = new Audio(soundDef.file);
      audio.volume = this.volume;
      audio.preload = 'auto';
      
      // Test if the audio can be loaded
      await new Promise((resolve, reject) => {
        audio.addEventListener('canplaythrough', resolve, { once: true });
        audio.addEventListener('error', reject, { once: true });
        audio.load();
      });
      
      this.sounds[soundKey] = audio;
      return audio;
    } catch (error) {
      console.warn(`🔇 Failed to load sound ${soundKey}:`, error);
      return null;
    }
  }
  
  /**
   * Play a sound by key
   */
  async play(soundKey, options = {}) {
    if (!this.isEnabled) return;
    
    const { 
      volume = this.volume, 
      force = false,
      showNotification = true 
    } = options;
    
    try {
      // Try to play the audio file
      let audio = this.sounds[soundKey];
      if (!audio && !force) {
        audio = await this.loadSound(soundKey);
      }
      
      if (audio) {
        audio.volume = Math.min(Math.max(volume, 0), 1);
        audio.currentTime = 0; // Reset to beginning
        await audio.play();
        
        if (showNotification) {
          this.showSoundNotification(soundKey);
        }
        return true;
      }
    } catch (error) {
      console.warn(`🔇 Failed to play sound ${soundKey}:`, error);
    }
    
    // Fallback to system sounds
    this.playFallbackSound(soundKey);
    return false;
  }
  
  /**
   * Play fallback system sound
   */
  playFallbackSound(soundKey) {
    const soundDef = this.soundDefinitions[soundKey];
    if (!soundDef) return;
    
    try {
      // Create a simple beep using Web Audio API
      if (soundDef.fallback === 'beep') {
        this.createBeep(800, 150); // 800Hz for 150ms
      } else if (soundDef.fallback === 'click') {
        this.createBeep(1200, 50); // 1200Hz for 50ms
      }
    } catch (error) {
      console.warn('🔇 Fallback sound failed:', error);
    }
  }
  
  /**
   * Create a beep sound using Web Audio API
   */
  createBeep(frequency = 800, duration = 150) {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = frequency;
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
    } catch (error) {
      console.warn('🔇 Web Audio API beep failed:', error);
    }
  }
  
  /**
   * Show visual notification for sound
   */
  showSoundNotification(soundKey) {
    const soundDef = this.soundDefinitions[soundKey];
    if (!soundDef) return;
    
    // Create a small visual indicator
    const notification = document.createElement('div');
    notification.className = 'sound-notification';
    notification.textContent = soundDef.description;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      background: rgba(22, 160, 133, 0.9);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      z-index: 10000;
      animation: soundNotificationSlide 2s ease-out forwards;
      pointer-events: none;
    `;
    
    // Add animation styles if not already present
    if (!document.getElementById('sound-notification-styles')) {
      const styles = document.createElement('style');
      styles.id = 'sound-notification-styles';
      styles.textContent = `
        @keyframes soundNotificationSlide {
          0% { transform: translateX(-100%); opacity: 0; }
          20% { transform: translateX(0); opacity: 1; }
          80% { transform: translateX(0); opacity: 1; }
          100% { transform: translateX(-100%); opacity: 0; }
        }
      `;
      document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Remove after animation
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 2000);
  }
  
  /**
   * Load user preferences from localStorage
   */
  loadPreferences() {
    try {
      const prefs = localStorage.getItem('icaldz-sound-preferences');
      if (prefs) {
        const parsed = JSON.parse(prefs);
        this.isEnabled = parsed.isEnabled !== false; // Default to true
        this.volume = parsed.volume || 0.7;
      }
    } catch (error) {
      console.warn('🔇 Failed to load sound preferences:', error);
    }
  }
  
  /**
   * Save user preferences to localStorage
   */
  savePreferences() {
    try {
      const prefs = {
        isEnabled: this.isEnabled,
        volume: this.volume
      };
      localStorage.setItem('icaldz-sound-preferences', JSON.stringify(prefs));
    } catch (error) {
      console.warn('🔇 Failed to save sound preferences:', error);
    }
  }
  
  /**
   * Toggle sound on/off
   */
  toggle() {
    this.isEnabled = !this.isEnabled;
    this.savePreferences();
    return this.isEnabled;
  }
  
  /**
   * Set volume (0-1)
   */
  setVolume(volume) {
    this.volume = Math.min(Math.max(volume, 0), 1);
    
    // Update all loaded sounds
    Object.values(this.sounds).forEach(audio => {
      if (audio) audio.volume = this.volume;
    });
    
    this.savePreferences();
  }
  
  /**
   * Get current status
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      volume: this.volume,
      isInitialized: this.isInitialized,
      loadedSounds: Object.keys(this.sounds).length
    };
  }
}

// Create singleton instance
export const SoundManager = new SoundManagerClass();

// Export for direct use
export default SoundManager;
