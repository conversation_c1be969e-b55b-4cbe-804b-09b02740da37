/**
 * Data Synchronization System for Multiple Servers
 * نظام مزامنة البيانات بين الخوادم المختلفة
 */

import { appConfig } from './config.js';

class DataSyncManager {
  constructor() {
    // Dynamic server configuration using appConfig
    this.currentServer = window.location.origin;
    this.servers = appConfig.getServerList();
    this.syncInterval = appConfig.get('servers.syncInterval') || 30000;
    this.requestTimeout = appConfig.get('servers.requestTimeout') || 5000;
    this.retryAttempts = appConfig.get('servers.retryAttempts') || 3;
    this.isOnline = navigator.onLine;
    this.syncTimer = null;
    this.lastSyncTime = null;

    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.startSync();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.stopSync();
    });

    // Listen for port changes
    this.detectPortChanges();
  }

  /**
   * Generate server list based on current port
   */
  generateServerList() {
    const currentUrl = new URL(window.location.origin);
    const currentPort = parseInt(currentUrl.port) || (currentUrl.protocol === 'https:' ? 443 : 80);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.hostname}`;

    // Generate alternative ports
    const ports = [3000, 3001, 3002, 3003, 5000, 5001, 8080, 8081];
    const servers = [];

    // Add current server first
    servers.push(this.currentServer);

    // Add alternative ports (excluding current)
    ports.forEach(port => {
      if (port !== currentPort) {
        servers.push(`${baseUrl}:${port}`);
      }
    });

    return [...new Set(servers)]; // Remove duplicates
  }

  /**
   * Detect port changes and update server list
   */
  detectPortChanges() {
    const originalOrigin = window.location.origin;
    setInterval(() => {
      if (window.location.origin !== originalOrigin) {
        this.currentServer = window.location.origin;
        this.servers = this.generateServerList();
        console.log('Port change detected, updated server list:', this.servers);
      }
    }, 5000);
  }

  /**
   * Start automatic synchronization process
   */
  startSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      this.syncAllData();
    }, this.syncInterval);

    // Initial sync
    this.syncAllData();
  }

  /**
   * Stop automatic synchronization process
   */
  stopSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * Synchronize all data
   */
  async syncAllData() {
    if (!this.isOnline) return;

    try {
      this.lastSyncTime = new Date().toISOString();
      localStorage.setItem('last-sync-time', this.lastSyncTime);

      const dataKeys = [
        'icaldz-sellers',
        'icaldz-products',
        'icaldz-customers',
        'icaldz-invoices',
        'icaldz-purchases',
        'icaldz-suppliers',
        'icaldz-settings',
        'icaldz-expenses'
      ];

      let syncedCount = 0;
      for (const key of dataKeys) {
        try {
          await this.syncDataKey(key);
          syncedCount++;
        } catch (error) {
          console.warn(`فشل في مزامنة ${key}:`, error);
        }
      }

      console.log(`Synced ${syncedCount} of ${dataKeys.length} data keys`);
    } catch (error) {
      console.error('Data synchronization error:', error);
    }
  }

  /**
   * مزامنة مفتاح بيانات محدد
   */
  async syncDataKey(key) {
    try {
      const localData = localStorage.getItem(key);
      const localTimestamp = localStorage.getItem(`${key}-timestamp`) || '0';

      // Get data from other servers
      const serverData = await this.getDataFromServers(key);

      // Find the most recent data
      let mostRecentData = { data: localData, timestamp: parseInt(localTimestamp), server: this.currentServer };

      for (const serverInfo of serverData) {
        if (serverInfo.timestamp > mostRecentData.timestamp) {
          mostRecentData = serverInfo;
        }
      }

      // Update local data if newer data found
      if (mostRecentData.server !== this.currentServer) {
        localStorage.setItem(key, mostRecentData.data);
        localStorage.setItem(`${key}-timestamp`, mostRecentData.timestamp.toString());

        // Trigger storage event for other tabs
        window.dispatchEvent(new StorageEvent('storage', {
          key: key,
          newValue: mostRecentData.data,
          oldValue: localData
        }));
      }

      // Broadcast to other servers if we have newer data
      if (mostRecentData.server === this.currentServer) {
        await this.broadcastToServers(key, localData, parseInt(localTimestamp));
      }

    } catch (error) {
      console.error(`خطأ في مزامنة ${key}:`, error);
    }
  }

  /**
   * الحصول على البيانات من الخوادم الأخرى
   */
  async getDataFromServers(key) {
    const promises = this.servers
      .filter(server => server !== this.currentServer)
      .map(async (server) => {
        try {
          // Create AbortController for timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

          const response = await fetch(`${server}/api/sync/${key}`, {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const result = await response.json();
            return {
              data: result.data,
              timestamp: result.timestamp,
              server: server,
              success: true
            };
          } else {
            console.warn(`خادم ${server} أرجع حالة: ${response.status}`);
          }
        } catch (error) {
          if (error.name === 'AbortError') {
            console.warn(`انتهت مهلة الاتصال بالخادم ${server}`);
          } else {
            console.warn(`فشل في الاتصال بالخادم ${server}:`, error.message);
          }
        }
        return null;
      });

    const results = await Promise.allSettled(promises);
    return results
      .filter(result => result.status === 'fulfilled' && result.value !== null)
      .map(result => result.value);
  }

  /**
   * إرسال البيانات إلى الخوادم الأخرى
   */
  async broadcastToServers(key, data, timestamp) {
    if (!this.isOnline) return;

    const promises = this.servers
      .filter(server => server !== this.currentServer)
      .map(async (server) => {
        try {
          // Create AbortController for timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

          const response = await fetch(`${server}/api/sync/${key}`, {
            method: 'POST',
            signal: controller.signal,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            },
            body: JSON.stringify({
              data: data,
              timestamp: timestamp,
              source: this.currentServer
            })
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              console.log(`تم إرسال البيانات بنجاح إلى ${server}`);
            }
          }
        } catch (error) {
          if (error.name === 'AbortError') {
            console.warn(`انتهت مهلة إرسال البيانات إلى ${server}`);
          } else {
            console.warn(`فشل في إرسال البيانات إلى ${server}:`, error.message);
          }
        }
      });

    await Promise.allSettled(promises);
  }

  /**
   * حفظ البيانات مع الطابع الزمني
   */
  saveData(key, data) {
    const timestamp = Date.now();
    localStorage.setItem(key, JSON.stringify(data));
    localStorage.setItem(`${key}-timestamp`, timestamp.toString());

    // Broadcast to other servers immediately
    this.broadcastToServers(key, JSON.stringify(data), timestamp);
  }

  /**
   * تحميل البيانات
   */
  loadData(key, defaultValue = null) {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
      console.error(`خطأ في تحميل البيانات ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * مزامنة فورية لمفتاح محدد
   */
  async forceSyncKey(key) {
    await this.syncDataKey(key);
  }

  /**
   * إعادة تعيين جميع البيانات (للطوارئ)
   */
  resetAllData() {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('icaldz-'));
    keys.forEach(key => localStorage.removeItem(key));

    // Reload page to reinitialize
    window.location.reload();
  }

  /**
   * الحصول على حالة المزامنة
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      isRunning: this.syncTimer !== null,
      currentServer: this.currentServer,
      servers: this.servers,
      lastSync: localStorage.getItem('last-sync-time') || 'لم يتم'
    };
  }
}

// إنشاء مثيل عام
export const dataSyncManager = new DataSyncManager();

// بدء المزامنة تلقائياً
if (typeof window !== 'undefined') {
  dataSyncManager.startSync();
}

export default DataSyncManager;
