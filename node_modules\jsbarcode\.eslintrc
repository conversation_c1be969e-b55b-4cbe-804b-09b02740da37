{"env": {"browser": true, "node": true}, "parserOptions": {"ecmaVersion": 7, "sourceType": "module", "ecmaFeatures": {"experimentalObjectRestSpread": true}}, "extends": "eslint:recommended", "predef": "j<PERSON><PERSON><PERSON>", "rules": {"curly": "error", "semi": [1, "always"], "no-console": 2, "no-control-regex": 0, "indent": [1, "tab", {"SwitchCase": 1}], "space-infix-ops": ["error"], "comma-spacing": ["error"]}}