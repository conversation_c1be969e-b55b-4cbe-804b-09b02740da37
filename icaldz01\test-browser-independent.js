/**
 * Test Browser-Independent Activation System
 * Demonstrates true hardware binding that works across browsers
 */

const BrowserIndependentActivation = require('./browser-independent-activation');
const SystemHardwareFingerprint = require('./system-hardware-fingerprint');

console.log('🔐 Browser-Independent Activation System Test');
console.log('==============================================');

async function testBrowserIndependentSystem() {
  try {
    const activation = new BrowserIndependentActivation();
    const fingerprinter = new SystemHardwareFingerprint();

    console.log('\n1. 🔍 Generating System Hardware Fingerprint...');
    const systemFingerprint = fingerprinter.generateSystemFingerprint();
    
    console.log('✅ System Hardware Fingerprint Generated:');
    console.log(`   Fingerprint: ${systemFingerprint.fingerprint}`);
    console.log(`   Platform: ${systemFingerprint.platform}`);
    console.log(`   Components: ${Object.keys(systemFingerprint.components).length} hardware components detected`);
    
    // Show some hardware details
    if (systemFingerprint.components.cpuInfo) {
      console.log(`   CPU: ${systemFingerprint.components.cpuInfo.model || 'Unknown'} (${systemFingerprint.components.cpuInfo.cores} cores)`);
    }
    if (systemFingerprint.components.memoryInfo) {
      console.log(`   Memory: ${systemFingerprint.components.memoryInfo.memoryRatio} GB`);
    }
    if (systemFingerprint.components.networkInfo && systemFingerprint.components.networkInfo.macAddresses) {
      console.log(`   Network: ${systemFingerprint.components.networkInfo.macAddresses.length} interfaces`);
    }

    console.log('\n2. 🔒 Generating Browser-Independent Activation Code...');
    const activationResult = activation.generateSystemActivationCode({
      clientName: 'zoka',
      machineId: 'F7C681E2C5959036',
      securityLevel: 'SYSTEM_BOUND',
      type: 'LIFETIME',
      bindToHardware: true
    });

    if (activationResult) {
      console.log('✅ Browser-Independent Code Generated Successfully!');
      console.log('==================================================');
      console.log(`🔑 Activation Code: ${activationResult.activationCode}`);
      console.log(`👤 Client: ${activationResult.clientName}`);
      console.log(`🆔 Client ID: ${activationResult.clientId}`);
      console.log(`🔒 Security Level: ${activationResult.securityLevel}`);
      console.log(`💻 Platform: ${activationResult.platform}`);
      console.log(`🔧 Hardware Bound: ${activationResult.hardwareBound ? 'YES' : 'NO'}`);
      console.log(`📅 Generated: ${activationResult.generatedAt}`);
      
      console.log('\n🛡️ Security Features:');
      Object.entries(activationResult.securityFeatures).forEach(([feature, enabled]) => {
        console.log(`   ${feature}: ${enabled ? '✅' : '❌'}`);
      });

      console.log('\n3. 🔍 Testing Code Validation...');
      
      // Test 1: Valid code with correct hardware
      console.log('\n   Test 1: Validating with correct hardware...');
      const validation1 = activation.validateSystemActivationCode(activationResult.activationCode, {
        machineId: 'F7C681E2C5959036'
      });
      
      if (validation1.valid) {
        console.log('   ✅ PASSED: Code validated successfully');
        console.log(`   Hardware Validation: ${validation1.hardwareValidation}`);
      } else {
        console.log('   ❌ FAILED: Code validation failed');
        console.log(`   Error: ${validation1.error}`);
      }

      // Test 2: Try to use the same code again (should fail)
      console.log('\n   Test 2: Testing one-time use protection...');
      
      // First, activate the system
      const activationAttempt = activation.activateSystem(activationResult.activationCode, {
        machineId: 'F7C681E2C5959036'
      });
      
      if (activationAttempt.success) {
        console.log('   ✅ System activated successfully');
        
        // Now try to use the same code again
        const validation2 = activation.validateSystemActivationCode(activationResult.activationCode, {
          machineId: 'F7C681E2C5959036'
        });
        
        if (!validation2.valid && validation2.error.includes('already used')) {
          console.log('   ✅ PASSED: One-time use protection working');
          console.log(`   Correctly rejected: ${validation2.error}`);
        } else {
          console.log('   ❌ FAILED: One-time use protection not working');
        }
      } else {
        console.log('   ❌ System activation failed');
        console.log(`   Error: ${activationAttempt.error}`);
      }

      console.log('\n4. 🔍 Testing System Activation Status...');
      const activationStatus = activation.checkSystemActivation();
      
      if (activationStatus.activated) {
        console.log('   ✅ System is properly activated');
        console.log(`   Hardware Validation: ${activationStatus.hardwareValidation.valid ? 'PASSED' : 'FAILED'}`);
        console.log(`   Confidence: ${(activationStatus.hardwareValidation.confidence * 100).toFixed(1)}%`);
      } else {
        console.log('   ❌ System activation check failed');
        console.log(`   Reason: ${activationStatus.reason}`);
      }

      console.log('\n5. 🧪 Generating Trial Code...');
      const trialCode = activation.generateSystemActivationCode({
        clientName: 'zoka-trial',
        securityLevel: 'SYSTEM_BOUND',
        type: 'TRIAL',
        trialDays: 7,
        bindToHardware: true
      });

      if (trialCode) {
        console.log('✅ Trial Code Generated Successfully!');
        console.log(`🔑 Trial Code: ${trialCode.activationCode}`);
        console.log(`⏰ Expires: ${trialCode.expiryDate}`);
        console.log(`🔧 Hardware Bound: ${trialCode.hardwareBound ? 'YES' : 'NO'}`);
      }

      console.log('\n🎉 Browser-Independent System Test Complete!');
      console.log('============================================');
      
      console.log('\n📋 SUMMARY - Your Browser-Independent Codes:');
      console.log('============================================');
      console.log(`🔒 System-Bound Code: ${activationResult.activationCode}`);
      if (trialCode) {
        console.log(`🧪 Trial Code: ${trialCode.activationCode}`);
      }
      
      console.log('\n🛡️ Key Security Improvements:');
      console.log('=============================');
      console.log('✅ TRUE Hardware Binding - Works at system level, not browser level');
      console.log('✅ Cross-Browser Protection - Same hardware fingerprint across all browsers');
      console.log('✅ Platform-Specific Security - Uses OS-level hardware identification');
      console.log('✅ CPU/Memory/Storage Binding - Binds to actual hardware components');
      console.log('✅ Network Interface Binding - Uses MAC addresses for identification');
      console.log('✅ BIOS/Motherboard Binding - Deep hardware-level identification');
      console.log('✅ One-Time Use Protection - Code cannot be reused once activated');
      console.log('✅ Private Browser Resistant - Works regardless of browser mode');
      
      console.log('\n🔧 How This Solves Your Problem:');
      console.log('================================');
      console.log('❌ OLD PROBLEM: Different browsers = Different fingerprints');
      console.log('✅ NEW SOLUTION: Same hardware = Same fingerprint across ALL browsers');
      console.log('');
      console.log('❌ OLD PROBLEM: Private/Incognito mode bypasses protection');
      console.log('✅ NEW SOLUTION: System-level binding works regardless of browser mode');
      console.log('');
      console.log('❌ OLD PROBLEM: Browser-based fingerprinting is unreliable');
      console.log('✅ NEW SOLUTION: Hardware-based fingerprinting is consistent and reliable');

    } else {
      console.log('❌ Failed to generate browser-independent activation code');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testBrowserIndependentSystem();
