@echo off
title Activation Code Validator - iCalDZ

echo.
echo ========================================================================
echo                   Activation Code Validator - iCalDZ
echo ========================================================================
echo.

if "%~1"=="" (
    set /p activation_code="Enter activation code to validate: "
) else (
    set activation_code=%~1
)

if "%activation_code%"=="" (
    echo ERROR: No activation code entered
    echo.
    echo TIP: You can use the command:
    echo    validate-code-en.bat "ICAL-2025-XXXX-XXXX-..."
    echo.
    pause
    exit /b 1
)

echo.
echo Validating activation code...
echo.

node generate-activation-code-en.js --validate "%activation_code%"

echo.
echo ========================================================================
pause
