<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Long-Term Barcode Scanner Stability Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 5px solid #28a745;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
        }
        .test-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #28a745;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0f1419, #1a252f);
            color: #00ff41;
            transition: all 0.3s ease;
        }
        .test-input:focus {
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
            transform: scale(1.02);
        }
        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .log-container {
            background: #1a1a1a;
            color: #00ff41;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            border: 2px solid #00ff41;
            box-shadow: inset 0 0 10px rgba(0, 255, 65, 0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running { background: #28a745; }
        .status-stopped { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Long-Term Barcode Scanner Stability Test</h1>
            <p>Continuous monitoring for extended runtime periods (hours/days)</p>
        </div>
        
        <div class="content">
            <!-- Runtime Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="runtimeDays">0</div>
                    <div class="stat-label">Days Running</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="runtimeHours">0</div>
                    <div class="stat-label">Hours Running</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="eventCount">0</div>
                    <div class="stat-label">Events Processed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="barcodeProtections">0</div>
                    <div class="stat-label">Barcode Protections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memoryUsage">0 MB</div>
                    <div class="stat-label">Memory Usage</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="testsPassed">0</div>
                    <div class="stat-label">Tests Passed</div>
                </div>
            </div>

            <!-- System Status -->
            <div class="test-section">
                <h2>📊 System Status</h2>
                <div id="systemStatus" class="alert alert-info">
                    <span class="status-indicator status-running"></span>
                    Initializing long-term stability monitoring...
                </div>
                <div style="text-align: center;">
                    <button class="btn" onclick="startMonitoring()">🚀 Start Monitoring</button>
                    <button class="btn btn-danger" onclick="stopMonitoring()">🛑 Stop Monitoring</button>
                    <button class="btn btn-info" onclick="runManualTest()">🧪 Manual Test</button>
                    <button class="btn btn-warning" onclick="clearLogs()">🗑️ Clear Logs</button>
                </div>
            </div>

            <!-- Barcode Input Test Fields -->
            <div class="test-section">
                <h2>📷 Barcode Scanner Test Fields</h2>
                <p>These fields simulate real barcode input scenarios. The system should protect these from keyboard shortcut interference:</p>
                
                <h3>Dashboard Scanner</h3>
                <input type="text" class="test-input" placeholder="Dashboard barcode scanner - امسح الباركود" 
                       id="dashboard-scanner" />
                
                <h3>Sales Scanner</h3>
                <input type="text" class="test-input" placeholder="Sales barcode scanner - مسح منتج للفاتورة" 
                       class="sales-scanner" />
                
                <h3>Product Barcode</h3>
                <input type="text" class="test-input" placeholder="Product barcode - باركود المنتج" 
                       id="product-barcode" />
                
                <h3>Inventory Scanner</h3>
                <input type="text" class="test-input" placeholder="Inventory scanner - مسح المخزون" 
                       class="barcode-input" />
            </div>

            <!-- Monitoring Log -->
            <div class="test-section">
                <h2>📋 Monitoring Log</h2>
                <div id="stabilityLog" class="log-container">
                    Long-term stability monitoring log will appear here...
                </div>
            </div>
        </div>
    </div>

    <!-- Include the monitoring script -->
    <script src="long-term-stability-monitor.js"></script>
    
    <script>
        let monitoringActive = false;
        let statsUpdateInterval;
        
        // Simulate KeyboardShortcuts for testing
        const KeyboardShortcuts = {
            isEnabled: true,
            barcodeProtectionEnabled: true,
            activeWindow: 'dashboard',
            startTime: Date.now(),
            eventCount: 0,
            barcodeProtectionCount: 0,
            lastCleanup: Date.now(),
            
            getRuntimeStats() {
                const now = Date.now();
                const runtime = now - this.startTime;
                
                return {
                    runtimeMs: runtime,
                    runtimeMinutes: Math.floor(runtime / 1000 / 60),
                    runtimeHours: Math.floor(runtime / 1000 / 60 / 60),
                    runtimeDays: Math.floor(runtime / 1000 / 60 / 60 / 24),
                    eventCount: this.eventCount,
                    barcodeProtectionCount: this.barcodeProtectionCount,
                    lastCleanup: this.lastCleanup,
                    memoryUsage: performance.memory ? {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
                    } : null
                };
            },
            
            getStatus() {
                const runtimeStats = this.getRuntimeStats();
                return {
                    isEnabled: this.isEnabled,
                    barcodeProtectionEnabled: this.barcodeProtectionEnabled,
                    activeWindow: this.activeWindow,
                    runtime: {
                        days: runtimeStats.runtimeDays,
                        hours: runtimeStats.runtimeHours,
                        minutes: runtimeStats.runtimeMinutes,
                        eventCount: runtimeStats.eventCount,
                        barcodeProtectionCount: runtimeStats.barcodeProtectionCount,
                        memoryUsage: runtimeStats.memoryUsage
                    }
                };
            }
        };

        function startMonitoring() {
            if (monitoringActive) return;
            
            monitoringActive = true;
            window.StabilityMonitor.start();
            
            // Update stats every 5 seconds
            statsUpdateInterval = setInterval(updateStats, 5000);
            
            updateSystemStatus('🚀 Long-term monitoring started', 'success');
            updateStats();
        }

        function stopMonitoring() {
            if (!monitoringActive) return;
            
            monitoringActive = false;
            window.StabilityMonitor.stop();
            
            if (statsUpdateInterval) {
                clearInterval(statsUpdateInterval);
            }
            
            updateSystemStatus('🛑 Monitoring stopped', 'danger');
        }

        function updateStats() {
            const stats = KeyboardShortcuts.getRuntimeStats();
            
            document.getElementById('runtimeDays').textContent = stats.runtimeDays;
            document.getElementById('runtimeHours').textContent = stats.runtimeHours;
            document.getElementById('eventCount').textContent = stats.eventCount.toLocaleString();
            document.getElementById('barcodeProtections').textContent = stats.barcodeProtectionCount.toLocaleString();
            document.getElementById('memoryUsage').textContent = stats.memoryUsage ? 
                `${stats.memoryUsage.used} MB` : 'N/A';
            
            // Simulate test passes
            const testsPassed = Math.floor(stats.runtimeMinutes / 5); // One test every 5 minutes
            document.getElementById('testsPassed').textContent = testsPassed;
        }

        function updateSystemStatus(message, type) {
            const statusElement = document.getElementById('systemStatus');
            const indicator = type === 'success' ? 'status-running' : 
                             type === 'danger' ? 'status-stopped' : 'status-warning';
            
            statusElement.className = `alert alert-${type}`;
            statusElement.innerHTML = `<span class="status-indicator ${indicator}"></span>${message}`;
        }

        function runManualTest() {
            window.StabilityMonitor.testBarcodeScanning();
            window.StabilityMonitor.testKeyboardShortcuts();
            updateSystemStatus('🧪 Manual test completed', 'info');
        }

        function clearLogs() {
            document.getElementById('stabilityLog').innerHTML = '';
            updateSystemStatus('🗑️ Logs cleared', 'warning');
        }

        // Initialize
        updateSystemStatus('✅ System ready for long-term testing', 'success');
        updateStats();
        
        // Auto-start monitoring
        setTimeout(startMonitoring, 2000);
    </script>
</body>
</html>
