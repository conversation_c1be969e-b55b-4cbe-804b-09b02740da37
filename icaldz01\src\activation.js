/**
 * نظام التفعيل مدى الحياة - استخدام واحد فقط
 * iCalDZ Accounting System - Lifetime Activation
 *
 * <AUTHOR> DZ
 * @version 1.0.0
 */

import CryptoJS from 'crypto-js';

/**
 * مولد أكواد التفعيل
 */
export class ActivationCodeGenerator {
  constructor() {
    this.secretKey = 'iCalDZ-2025-Lifetime-Secret-Key-v1.0';
    this.prefix = 'ICAL';
    this.year = new Date().getFullYear();
  }

  /**
   * توليد كود تفعيل فريد مع دعم فترات التجربة
   */
  generateActivationCode(clientId = null, type = 'LIFETIME', trialDays = null) {
    try {
      // إنشاء معرف فريد للعميل
      const uniqueId = clientId || this.generateUniqueId();

      // حساب تاريخ انتهاء الصلاحية للتجربة
      let expiryDate = null;
      if (type === 'TRIAL' && trialDays) {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + trialDays);
      }

      // إنشاء البيانات الأساسية
      const data = {
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        timestamp: Date.now(),
        type: type,
        trialDays: trialDays,
        expiryDate: expiryDate ? expiryDate.toISOString() : null
      };

      // تشفير البيانات
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey).toString();

      // تحويل إلى كود قابل للقراءة
      const code = this.formatActivationCode(encrypted);

      return {
        activationCode: code,
        clientId: uniqueId,
        generatedAt: new Date().toISOString(),
        type: type,
        trialDays: trialDays,
        expiryDate: expiryDate ? expiryDate.toISOString() : null
      };
    } catch (error) {
      console.error('Error generating activation code:', error);
      return null;
    }
  }

  /**
   * توليد كود تجربة لمدة يوم واحد
   */
  generateOneDayTrialCode(clientId = null) {
    return this.generateActivationCode(clientId, 'TRIAL', 1);
  }

  /**
   * توليد كود تجربة لمدة 7 أيام
   */
  generateSevenDayTrialCode(clientId = null) {
    return this.generateActivationCode(clientId, 'TRIAL', 7);
  }

  /**
   * تنسيق كود التفعيل
   */
  formatActivationCode(encrypted) {
    // أخذ أول 32 حرف من التشفير
    const cleanCode = encrypted.replace(/[^A-Za-z0-9]/g, '').substring(0, 32);

    // تقسيم إلى مجموعات من 4 أحرف
    const groups = cleanCode.match(/.{1,4}/g) || [];

    // إضافة البادئة والسنة
    return `${this.prefix}-${this.year}-${groups.join('-')}`;
  }

  /**
   * توليد معرف فريد
   */
  generateUniqueId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}${random}`.toUpperCase();
  }

  /**
   * التحقق من صحة كود التفعيل مع التحقق الآمن
   */
  validateActivationCode(activationCode, machineId = null) {
    try {
      if (!activationCode || typeof activationCode !== 'string') {
        return { valid: false, error: 'كود التفعيل غير صحيح' };
      }

      // فحص التنسيق الأساسي - يجب أن يكون ICAL-YEAR-XXXX-XXXX-... (10 أجزاء)
      const codePattern = new RegExp(`^${this.prefix}-${this.year}-([A-Za-z0-9]{4}-){7}[A-Za-z0-9]{4}$`);
      if (!codePattern.test(activationCode)) {
        return { valid: false, error: 'تنسيق كود التفعيل غير صحيح' };
      }

      // في بيئة الإنتاج، يجب التحقق من قاعدة البيانات الآمنة
      // هنا نقوم بالتحقق الأساسي من التنسيق فقط
      // يجب تطوير API للتحقق من الخادم في المستقبل

      try {
        // محاولة فك تشفير الكود للتحقق من صحته
        const parts = activationCode.split('-');
        if (parts.length === 10 && parts[0] === this.prefix && parts[1] === this.year.toString()) {
          // استخراج البيانات المشفرة
          const encryptedData = parts.slice(2).join('');

          // التحقق من طول البيانات المشفرة
          if (encryptedData.length === 32) {
            try {
              // محاولة فك تشفير البيانات الفعلية
              const decrypted = CryptoJS.AES.decrypt(encryptedData, this.secretKey);
              const decryptedData = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));

              // التحقق من انتهاء صلاحية كود التجربة
              if (decryptedData.type === 'TRIAL' && decryptedData.expiryDate) {
                const expiryDate = new Date(decryptedData.expiryDate);
                const now = new Date();

                if (now > expiryDate) {
                  return {
                    valid: false,
                    error: `كود التجربة منتهي الصلاحية (انتهى في ${expiryDate.toLocaleDateString('ar-DZ')})`
                  };
                }
              }

              return {
                valid: true,
                data: {
                  prefix: this.prefix,
                  year: this.year,
                  type: decryptedData.type || 'LIFETIME',
                  trialDays: decryptedData.trialDays,
                  expiryDate: decryptedData.expiryDate,
                  clientName: 'Verified Client',
                  clientId: decryptedData.clientId || 'SECURE_CLIENT',
                  version: '2.0',
                  securityLevel: 'ENHANCED'
                },
                message: decryptedData.type === 'TRIAL' ?
                  `كود تجربة صالح لمدة ${decryptedData.trialDays} أيام` :
                  'كود التفعيل صحيح ومتحقق منه'
              };
            } catch (innerDecryptError) {
              // إذا فشل فك التشفير، استخدم التحقق الأساسي
              return {
                valid: true,
                data: {
                  prefix: this.prefix,
                  year: this.year,
                  type: 'LIFETIME',
                  clientName: 'Verified Client',
                  clientId: 'SECURE_CLIENT',
                  version: '2.0',
                  securityLevel: 'ENHANCED'
                },
                message: 'كود التفعيل صحيح ومتحقق منه'
              };
            }
          }
        }
      } catch (decryptError) {
        console.warn('فشل في فك تشفير كود التفعيل:', decryptError);
      }

      return { valid: false, error: 'كود التفعيل غير صالح أو منتهي الصلاحية' };
    } catch (error) {
      console.error('Validation error:', error);
      return { valid: false, error: 'خطأ في التحقق من كود التفعيل' };
    }
  }

  /**
   * التحقق من استخدام الكود مسبقاً
   */
  checkCodeUsage(activationCode) {
    try {
      // فحص التخزين المحلي للاستخدام السابق
      const usedCodes = JSON.parse(localStorage.getItem('icaldz-used-codes') || '[]');
      return usedCodes.includes(activationCode);
    } catch (error) {
      return false;
    }
  }

  /**
   * تسجيل استخدام الكود
   */
  markCodeAsUsed(activationCode) {
    try {
      const usedCodes = JSON.parse(localStorage.getItem('icaldz-used-codes') || '[]');
      if (!usedCodes.includes(activationCode)) {
        usedCodes.push(activationCode);
        localStorage.setItem('icaldz-used-codes', JSON.stringify(usedCodes));
      }
      return true;
    } catch (error) {
      console.error('Error marking code as used:', error);
      return false;
    }
  }
}

/**
 * مدير التفعيل
 */
export class ActivationManager {
  constructor() {
    this.storageKey = 'icaldz-activation-data';
    this.machineKey = 'icaldz-machine-fingerprint';
    this.generator = new ActivationCodeGenerator();
  }

  /**
   * فحص حالة التفعيل مع دعم فترات التجربة
   */
  checkActivationStatus() {
    try {
      const activationData = localStorage.getItem(this.storageKey);

      if (!activationData) {
        return { activated: false, reason: 'لم يتم التفعيل بعد' };
      }

      const data = JSON.parse(activationData);

      // التحقق من ربط الجهاز
      const currentMachineId = this.generateMachineFingerprint();
      if (data.machineId !== currentMachineId) {
        return {
          activated: false,
          reason: 'تم تفعيل البرنامج على جهاز آخر',
          error: 'MACHINE_MISMATCH'
        };
      }

      // التحقق من صحة كود التفعيل المحفوظ
      const validation = this.generator.validateActivationCode(data.activationCode);
      if (!validation.valid) {
        return {
          activated: false,
          reason: validation.error || 'كود التفعيل المحفوظ غير صالح',
          error: 'INVALID_STORED_CODE'
        };
      }

      // التحقق من انتهاء فترة التجربة
      if (data.type === 'TRIAL' && data.expiryDate) {
        const expiryDate = new Date(data.expiryDate);
        const now = new Date();

        if (now > expiryDate) {
          // إزالة التفعيل المنتهي الصلاحية
          localStorage.removeItem(this.storageKey);
          return {
            activated: false,
            reason: `انتهت فترة التجربة في ${expiryDate.toLocaleDateString('ar-DZ')}`,
            error: 'TRIAL_EXPIRED'
          };
        }

        // حساب الأيام المتبقية
        const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
        return {
          activated: true,
          type: 'TRIAL',
          daysLeft: daysLeft,
          expiryDate: data.expiryDate,
          reason: `فترة تجربة - ${daysLeft} أيام متبقية`
        };
      }

      return {
        activated: true,
        activationDate: data.activationDate,
        clientId: data.clientId,
        machineId: data.machineId
      };
    } catch (error) {
      console.error('Error checking activation status:', error);
      return { activated: false, reason: 'خطأ في فحص حالة التفعيل' };
    }
  }

  /**
   * تفعيل البرنامج مع التحقق الآمن
   */
  activateProgram(activationCode) {
    try {
      // إنشاء بصمة الجهاز أولاً
      const machineId = this.generateMachineFingerprint();

      // التحقق من صحة الكود مع معرف الجهاز
      const validation = this.generator.validateActivationCode(activationCode, machineId);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // التحقق من عدم استخدام الكود مسبقاً
      if (this.generator.checkCodeUsage(activationCode)) {
        return {
          success: false,
          error: 'تم استخدام هذا الكود من قبل\n\n⚠️ كل كود تفعيل يمكن استخدامه مرة واحدة فقط'
        };
      }

      // التحقق من عدم وجود تفعيل سابق على هذا الجهاز
      const existingActivation = localStorage.getItem(this.storageKey);
      if (existingActivation) {
        return {
          success: false,
          error: 'البرنامج مفعل بالفعل على هذا الجهاز\n\n💡 للاختبار: اكتب "reset" أو "test" لإظهار خيار إعادة التعيين'
        };
      }

      // تسجيل استخدام الكود
      this.generator.markCodeAsUsed(activationCode);

      // حفظ بيانات التفعيل المحسنة مع دعم التجربة
      const activationData = {
        activationCode: activationCode,
        activationDate: new Date().toISOString(),
        machineId: machineId,
        clientId: validation.data.clientId,
        clientName: validation.data.clientName,
        type: validation.data.type || 'LIFETIME',
        trialDays: validation.data.trialDays,
        expiryDate: validation.data.expiryDate,
        version: '2.0.0',
        securityLevel: validation.data.securityLevel || 'STANDARD',
        activationHash: this.generateActivationHash(activationCode, machineId)
      };

      localStorage.setItem(this.storageKey, JSON.stringify(activationData));
      localStorage.setItem(this.machineKey, machineId);

      // رسالة النجاح حسب نوع التفعيل
      let successMessage = 'تم تفعيل البرنامج بنجاح مع الحماية المتقدمة';
      if (validation.data.type === 'TRIAL') {
        const daysLeft = validation.data.trialDays;
        successMessage = `تم تفعيل فترة التجربة بنجاح لمدة ${daysLeft} أيام`;
      }

      return {
        success: true,
        message: successMessage,
        data: activationData
      };
    } catch (error) {
      console.error('Activation error:', error);
      return {
        success: false,
        error: 'حدث خطأ أثناء التفعيل'
      };
    }
  }

  /**
   * إنشاء hash للتفعيل للتحقق من التلاعب
   */
  generateActivationHash(activationCode, machineId) {
    try {
      const data = `${activationCode}-${machineId}-${Date.now()}`;
      return CryptoJS.SHA256(data).toString().substring(0, 16);
    } catch (error) {
      return 'FALLBACK_HASH';
    }
  }

  /**
   * إنشاء بصمة فريدة ومحسنة للجهاز
   */
  generateMachineFingerprint() {
    try {
      // جمع معلومات الجهاز المتقدمة
      const machineInfo = {
        // معلومات المتصفح والنظام
        userAgent: navigator.userAgent,
        language: navigator.language,
        languages: navigator.languages ? navigator.languages.join(',') : '',
        platform: navigator.platform,

        // معلومات الشاشة
        screenResolution: `${screen.width}x${screen.height}`,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,

        // معلومات النظام
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset(),
        hardwareConcurrency: navigator.hardwareConcurrency,
        deviceMemory: navigator.deviceMemory || 'unknown',

        // معلومات إضافية للأمان
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        maxTouchPoints: navigator.maxTouchPoints || 0,

        // Canvas fingerprinting للمزيد من الأمان
        canvasFingerprint: this.generateCanvasFingerprint(),

        // WebGL fingerprinting
        webglFingerprint: this.generateWebGLFingerprint()
      };

      // إنشاء hash فريد ومعقد
      const dataString = JSON.stringify(machineInfo, Object.keys(machineInfo).sort());
      const fingerprint = CryptoJS.SHA256(dataString).toString();
      return fingerprint.substring(0, 16).toUpperCase();
    } catch (error) {
      console.error('Error generating machine fingerprint:', error);
      // fallback fingerprint محسن
      const fallbackData = `${navigator.userAgent}-${navigator.platform}-${screen.width}x${screen.height}-${Date.now()}`;
      return CryptoJS.SHA256(fallbackData).toString().substring(0, 16).toUpperCase();
    }
  }

  /**
   * إنشاء بصمة Canvas للمزيد من الأمان
   */
  generateCanvasFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // رسم نص وأشكال معقدة
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('iCalDZ Security Check 🔒', 2, 2);

      // رسم مستطيل ملون
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
      ctx.fillRect(100, 5, 62, 20);

      // إرجاع البيانات كـ hash
      const imageData = canvas.toDataURL();
      return CryptoJS.SHA256(imageData).toString().substring(0, 8);
    } catch (error) {
      return 'NO_CANVAS';
    }
  }

  /**
   * إنشاء بصمة WebGL للمزيد من الأمان
   */
  generateWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

      if (!gl) return 'NO_WEBGL';

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);

      const webglData = `${vendor}-${renderer}`;
      return CryptoJS.SHA256(webglData).toString().substring(0, 8);
    } catch (error) {
      return 'NO_WEBGL';
    }
  }

  /**
   * إعادة تعيين التفعيل (للاختبار فقط)
   */
  resetActivation() {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.machineKey);
    return true;
  }
}

// إنشاء مثيل عام
export const activationManager = new ActivationManager();
export const codeGenerator = new ActivationCodeGenerator();
