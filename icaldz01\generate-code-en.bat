@echo off
title iCalDZ Activation Code Generator

:MAIN_MENU
cls
echo.
echo ========================================================================
echo                    iCalDZ Activation Code Generator
echo                      Lifetime License System
echo ========================================================================
echo.
echo  [1] Generate New Activation Code
echo  [2] Validate Activation Code
echo  [3] Generate Multiple Codes
echo  [4] Show Help
echo  [5] Exit
echo.
echo ========================================================================
echo.
set /p choice="Choose option (1-5): "

if "%choice%"=="1" goto GENERATE_SINGLE
if "%choice%"=="2" goto VALIDATE_CODE
if "%choice%"=="3" goto GENERATE_MULTIPLE
if "%choice%"=="4" goto SHOW_HELP
if "%choice%"=="5" goto EXIT
goto INVALID_CHOICE

:GENERATE_SINGLE
cls
echo.
echo ========================================================================
echo                     Generate New Activation Code
echo ========================================================================
echo.
set /p client_name="Enter client name (optional): "

echo.
echo Generating activation code...
echo.

if "%client_name%"=="" (
    node generate-activation-code-en.js
) else (
    node generate-activation-code-en.js "%client_name%"
)

echo.
echo ========================================================================
pause
goto MAIN_MENU

:VALIDATE_CODE
cls
echo.
echo ========================================================================
echo                       Validate Activation Code
echo ========================================================================
echo.
set /p activation_code="Enter activation code to validate: "

if "%activation_code%"=="" (
    echo ERROR: Please enter an activation code
    pause
    goto MAIN_MENU
)

echo.
echo Validating activation code...
echo.

node generate-activation-code-en.js --validate "%activation_code%"

echo.
echo ========================================================================
pause
goto MAIN_MENU

:GENERATE_MULTIPLE
cls
echo.
echo ========================================================================
echo                         Generate Multiple Codes
echo ========================================================================
echo.
set /p count="How many activation codes do you need? (1-50): "

if "%count%"=="" set count=1
if %count% LSS 1 set count=1
if %count% GTR 50 set count=50

echo.
echo Generating %count% activation codes...
echo.

for /L %%i in (1,1,%count%) do (
    echo ========================================================================
    echo                              Code #%%i
    echo ========================================================================
    node generate-activation-code-en.js "Client #%%i"
    echo.
)

echo.
echo SUCCESS: Generated %count% activation codes!
echo ========================================================================
pause
goto MAIN_MENU

:SHOW_HELP
cls
echo.
echo ========================================================================
echo                                  Help
echo ========================================================================
echo.
echo About:
echo    This program generates lifetime activation codes for iCalDZ Accounting System
echo.
echo Activation Code Features:
echo    - Each code can only be used once
echo    - Activation is tied to the device used
echo    - Valid for lifetime
echo    - Encrypted with SHA-256 technology
echo.
echo Contact Information:
echo    Phone: +213 551 93 05 89
echo    Email: <EMAIL>
echo    Website: www.icodedz.com
echo.
echo Tips:
echo    - Keep activation codes in a safe place
echo    - Do not share codes with unauthorized persons
echo    - Verify code validity before sending to client
echo.
echo ========================================================================
pause
goto MAIN_MENU

:INVALID_CHOICE
cls
echo.
echo ERROR: Invalid choice! Please choose a number from 1 to 5
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ========================================================================
echo                             Thank You
echo                    iCalDZ Activation Code Generator
echo ========================================================================
echo.
echo Goodbye!
echo.
timeout /t 2 >nul
exit
