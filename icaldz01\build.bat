@echo off
title Building iCalDZ Installer
color 0A

REM Change to script directory
cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════╗
echo ║                Building iCalDZ Installer                 ║
echo ║                    Developed by iCode DZ                 ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo 📁 Working directory: %CD%
echo.

echo 🔄 Step 1: Killing processes...
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo 🧹 Step 2: Cleaning old files...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "release" rmdir /s /q "release" >nul 2>&1
timeout /t 2 /nobreak >nul

echo 🔄 Step 3: Building React app...
call npm run build
if errorlevel 1 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo.
echo 📦 Step 4: Creating installer...
call npm run dist
if errorlevel 1 (
    echo ❌ Installer creation failed!
    echo.
    echo 💡 Solutions:
    echo 1. Restart computer
    echo 2. Run as Administrator
    echo 3. Disable antivirus temporarily
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo 📁 Your installer is in: build\
echo 🚀 App works immediately after installation - no activation needed!
echo.
echo 📞 Support: +213 551 930 589
echo 📧 Email: <EMAIL>
echo.
pause
