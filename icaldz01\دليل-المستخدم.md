# 📖 دليل المستخدم - نظام المحاسبي

## 🚀 طرق تشغيل النظام

### 1. للمطورين (وضع التطوير)
```bash
# تثبيت المكتبات (مرة واحدة فقط)
npm install

# تشغيل النظام
npm run dev
```

### 2. للمستخدمين (تشغيل مباشر)
- انقر نقرة مزدوجة على ملف `run-system.bat`
- سيتم تشغيل النظام تلقائياً في المتصفح

### 3. تطبيق سطح المكتب (الأفضل)
```bash
# بناء التطبيق
npm run dist

# ستجد ملف .exe في مجلد release
# يعمل بدون تثبيت إضافي
```

## 🎯 مميزات كل طريقة

### وضع التطوير:
- ✅ سريع للتطوير والتعديل
- ✅ تحديث مباشر للتغييرات
- ❌ يحتاج Node.js مثبت
- ❌ يظهر الكود المصدري

### التشغيل المباشر:
- ✅ سهل للمستخدمين
- ✅ ملف واحد للتشغيل
- ❌ يحتاج Node.js مثبت
- ❌ يظهر الكود المصدري

### تطبيق سطح المكتب:
- ✅ لا يحتاج Node.js
- ✅ الكود محمي ومشفر
- ✅ يعمل كبرنامج حقيقي
- ✅ سهل التوزيع
- ✅ مظهر احترافي

## 🔒 الحماية والأمان

### مستويات الحماية:

#### المستوى الأساسي (وضع التطوير):
- الكود مكشوف
- يمكن النسخ والتعديل
- مناسب للتطوير فقط

#### المستوى المتوسط (تشغيل مباشر):
- الكود في ملفات منفصلة
- صعوبة متوسطة في النسخ
- مناسب للاستخدام الداخلي

#### المستوى العالي (تطبيق سطح المكتب):
- الكود مشفر ومحمي
- صعوبة عالية في النسخ
- مناسب للتوزيع التجاري

## 📦 التوزيع للعملاء

### الطريقة الأولى: ملف قابل للتشغيل (الأفضل)
1. قم ببناء التطبيق: `npm run dist`
2. أرسل ملف `.exe` من مجلد `release`
3. العميل ينقر نقرة مزدوجة
4. يعمل مباشرة بدون إعدادات

### الطريقة الثانية: مجلد كامل
1. انسخ المجلد كاملاً
2. تأكد من وجود ملف `run-system.bat`
3. العميل ينقر على الملف
4. يحتاج Node.js مثبت

### الطريقة الثالثة: رابط ويب
1. رفع النظام على خادم
2. إعطاء رابط للعميل
3. يعمل من أي متصفح
4. يحتاج إنترنت دائم

## 🛡️ حماية إضافية

### إضافة رقم تسلسلي:
```javascript
// في بداية التطبيق
const serialNumber = "ICODE-2025-XXXX";
if (!validateSerial(serialNumber)) {
  alert("رقم تسلسلي غير صحيح");
  return;
}
```

### إضافة تاريخ انتهاء:
```javascript
// فحص تاريخ الانتهاء
const expiryDate = new Date("2025-12-31");
if (new Date() > expiryDate) {
  alert("انتهت صلاحية النظام");
  return;
}
```

### ربط بجهاز معين:
```javascript
// فحص معرف الجهاز
const machineId = getMachineId();
const allowedMachines = ["MACHINE-ID-1", "MACHINE-ID-2"];
if (!allowedMachines.includes(machineId)) {
  alert("غير مصرح لهذا الجهاز");
  return;
}
```

## 📞 الدعم التقني

### للمساعدة في:
- تثبيت النظام
- حل المشاكل التقنية
- إضافة ميزات جديدة
- التدريب على الاستخدام

### معلومات الاتصال:
- **الهاتف:** +213 551 93 05 89
- **البريد:** <EMAIL>
- **الموقع:** www.icodedz.com

### ساعات العمل:
- السبت - الخميس: 8:00 ص - 6:00 م
- الجمعة: مغلق
- التوقيت: توقيت الجزائر (GMT+1)

---
**© 2025 iCode DZ - جميع الحقوق محفوظة**
