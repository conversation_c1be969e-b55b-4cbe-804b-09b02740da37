{"name": "wait-on", "description": "wait-on is a cross platform command line utility and Node.js API which will wait for files, ports, sockets, and http(s) resources to become available", "version": "7.2.0", "main": "lib/wait-on", "bin": {"wait-on": "bin/wait-on"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/jeffbski/wait-on.git"}, "bugs": {"url": "http://github.com/jeffbski/wait-on/issues"}, "license": "MIT", "scripts": {"lint": "eslint \"lib/**/*.js\" \"test/**/*.js\" \"bin/wait-on\"", "publish:next": "npm publish --tag next && npm view", "test": "mocha --exit 'test/**/*.mocha.js'"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "expect-legacy": "^1.20.2", "mkdirp": "^1.0.4", "mocha": "^10.2.0", "temp": "^0.9.4"}, "dependencies": {"axios": "^1.6.1", "joi": "^17.11.0", "lodash": "^4.17.21", "minimist": "^1.2.8", "rxjs": "^7.8.1"}, "keywords": ["wait", "delay", "cli", "files", "tcp", "ports", "sockets", "http", "exist", "ready", "available", "portable", "cross-platform", "unix", "linux", "windows", "win32", "osx"]}