/**
 * Browser-Independent Activation System
 * True hardware binding that works across all browsers
 * 
 * <AUTHOR> DZ
 * @version 3.0.0
 */

const SystemHardwareFingerprint = require('./system-hardware-fingerprint');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class BrowserIndependentActivation {
  constructor() {
    this.prefix = 'ICAL';
    this.year = 2025;
    this.secretKey = 'iCalDZ-2025-System-Level-Security-v3.0';
    this.hardwareFingerprinter = new SystemHardwareFingerprint();
    this.activationFile = path.join(__dirname, 'system-activation-data.json');
    this.usedCodesFile = path.join(__dirname, 'used-codes-system.json');
  }

  /**
   * Generate system-level activation code
   */
  generateSystemActivationCode(options = {}) {
    try {
      const {
        clientName = 'Licensed User',
        machineId = null,
        securityLevel = 'SYSTEM_BOUND',
        type = 'LIFETIME',
        trialDays = null,
        bindToHardware = true
      } = options;

      // Generate system hardware fingerprint
      const hardwareFingerprint = this.hardwareFingerprinter.generateSystemFingerprint();
      
      // Generate unique client ID
      const uniqueId = this.generateUniqueId();
      
      // Calculate expiry for trial codes
      let expiryDate = null;
      if (type === 'TRIAL' && trialDays) {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + trialDays);
      }

      // Create activation data
      const activationData = {
        prefix: this.prefix,
        year: this.year,
        clientId: uniqueId,
        clientName,
        machineId,
        type,
        trialDays,
        expiryDate: expiryDate ? expiryDate.toISOString() : null,
        securityLevel,
        bindToHardware,
        
        // System-level security data
        systemFingerprint: hardwareFingerprint.fingerprint,
        hardwareComponents: bindToHardware ? hardwareFingerprint.components : null,
        platform: hardwareFingerprint.platform,
        
        // Security metadata
        timestamp: Date.now(),
        generatedAt: new Date().toISOString(),
        version: '3.0'
      };

      // Generate activation code
      const codeHash = this.generateActivationHash(activationData);
      const activationCode = this.formatActivationCode(codeHash);
      
      // Store activation data
      this.storeActivationData(activationCode, activationData);
      
      return {
        activationCode,
        clientId: uniqueId,
        clientName,
        type,
        securityLevel,
        generatedAt: activationData.generatedAt,
        expiryDate: activationData.expiryDate,
        systemFingerprint: hardwareFingerprint.fingerprint,
        platform: hardwareFingerprint.platform,
        hardwareBound: bindToHardware,
        securityFeatures: {
          systemLevelBinding: true,
          hardwareFingerprinting: bindToHardware,
          crossBrowserProtection: true,
          platformSpecific: true,
          machineBinding: !!machineId,
          oneTimeUse: true
        }
      };
    } catch (error) {
      console.error('System activation code generation error:', error);
      return null;
    }
  }

  /**
   * Validate system-level activation code
   */
  validateSystemActivationCode(activationCode, options = {}) {
    try {
      const { tolerance = 0.8 } = options;
      
      // Check format
      if (!activationCode.startsWith(`${this.prefix}-${this.year}-`)) {
        return { valid: false, error: 'Invalid code format' };
      }

      // Load stored activation data
      const storedData = this.getStoredActivationData(activationCode);
      if (!storedData) {
        return { valid: false, error: 'Code not found or invalid' };
      }

      // Check if code is already used
      if (storedData.used) {
        return { 
          valid: false, 
          error: 'Code already used',
          usedAt: storedData.usedAt,
          usedBy: storedData.usedBy
        };
      }

      // Check expiry for trial codes
      if (storedData.type === 'TRIAL' && storedData.expiryDate) {
        if (new Date() > new Date(storedData.expiryDate)) {
          return { 
            valid: false, 
            error: 'Trial code expired',
            expiredAt: storedData.expiryDate
          };
        }
      }

      // Validate system hardware fingerprint
      if (storedData.bindToHardware) {
        const currentFingerprint = this.hardwareFingerprinter.generateSystemFingerprint();
        const fingerprintValidation = this.hardwareFingerprinter.validateFingerprint(
          storedData.systemFingerprint, 
          tolerance
        );
        
        if (!fingerprintValidation.valid) {
          return {
            valid: false,
            error: 'Hardware fingerprint mismatch - code bound to different hardware',
            details: fingerprintValidation,
            storedFingerprint: storedData.systemFingerprint,
            currentFingerprint: currentFingerprint.fingerprint
          };
        }
        
        // Log fingerprint validation details
        console.log('Hardware fingerprint validation:', fingerprintValidation);
      }

      // Validate machine ID if provided
      if (storedData.machineId && options.machineId) {
        if (storedData.machineId !== options.machineId) {
          return {
            valid: false,
            error: 'Machine ID mismatch',
            expected: storedData.machineId,
            provided: options.machineId
          };
        }
      }

      return {
        valid: true,
        data: storedData,
        securityLevel: storedData.securityLevel,
        message: 'System-level activation code validated successfully',
        hardwareValidation: storedData.bindToHardware ? 'passed' : 'not_required'
      };
    } catch (error) {
      console.error('System validation error:', error);
      return { 
        valid: false, 
        error: 'Validation error occurred',
        details: error.message
      };
    }
  }

  /**
   * Activate the system with hardware binding
   */
  activateSystem(activationCode, options = {}) {
    try {
      // Validate the code first
      const validation = this.validateSystemActivationCode(activationCode, options);
      if (!validation.valid) {
        return { success: false, error: validation.error, details: validation };
      }

      // Get current system fingerprint
      const currentFingerprint = this.hardwareFingerprinter.generateSystemFingerprint();
      
      // Mark code as used
      this.markCodeAsUsed(activationCode, {
        usedAt: new Date().toISOString(),
        usedBy: currentFingerprint.fingerprint,
        platform: currentFingerprint.platform,
        activationDetails: {
          hostname: require('os').hostname(),
          userAgent: 'System-Level-Activation',
          timestamp: Date.now()
        }
      });

      // Store activation in system
      const activationInfo = {
        activationCode,
        activatedAt: new Date().toISOString(),
        systemFingerprint: currentFingerprint.fingerprint,
        hardwareComponents: currentFingerprint.components,
        platform: currentFingerprint.platform,
        clientData: validation.data,
        securityLevel: validation.data.securityLevel,
        type: validation.data.type,
        expiryDate: validation.data.expiryDate
      };

      this.storeSystemActivation(activationInfo);

      return {
        success: true,
        message: 'System activated successfully with hardware binding',
        activationInfo,
        securityFeatures: {
          systemLevelBinding: true,
          hardwareFingerprinting: true,
          crossBrowserProtection: true,
          platformSpecific: true
        }
      };
    } catch (error) {
      console.error('System activation error:', error);
      return {
        success: false,
        error: 'System activation failed',
        details: error.message
      };
    }
  }

  /**
   * Check if system is activated
   */
  checkSystemActivation() {
    try {
      const activationData = this.getSystemActivation();
      if (!activationData) {
        return { activated: false, reason: 'System not activated' };
      }

      // Validate current hardware against stored fingerprint
      const currentFingerprint = this.hardwareFingerprinter.generateSystemFingerprint();
      const fingerprintValidation = this.hardwareFingerprinter.validateFingerprint(
        activationData.systemFingerprint,
        0.8
      );

      if (!fingerprintValidation.valid) {
        return {
          activated: false,
          reason: 'Hardware fingerprint mismatch',
          details: fingerprintValidation,
          storedFingerprint: activationData.systemFingerprint,
          currentFingerprint: currentFingerprint.fingerprint
        };
      }

      // Check trial expiry
      if (activationData.type === 'TRIAL' && activationData.expiryDate) {
        if (new Date() > new Date(activationData.expiryDate)) {
          return {
            activated: false,
            reason: 'Trial period expired',
            expiredAt: activationData.expiryDate
          };
        }
      }

      return {
        activated: true,
        activationData,
        hardwareValidation: fingerprintValidation,
        message: 'System is properly activated and hardware-bound'
      };
    } catch (error) {
      console.error('Activation check error:', error);
      return {
        activated: false,
        reason: 'Activation check failed',
        error: error.message
      };
    }
  }

  /**
   * Generate activation hash
   */
  generateActivationHash(data) {
    const dataString = JSON.stringify(data, Object.keys(data).sort());
    return crypto.createHash('sha256').update(dataString + this.secretKey).digest('hex');
  }

  /**
   * Format activation code
   */
  formatActivationCode(hash) {
    const code = hash.substring(0, 32).toUpperCase();
    const groups = code.match(/.{1,4}/g) || [];
    return `${this.prefix}-${this.year}-${groups.join('-')}`;
  }

  /**
   * Generate unique ID
   */
  generateUniqueId() {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}${random}`.toUpperCase();
  }

  /**
   * Store activation data
   */
  storeActivationData(activationCode, data) {
    try {
      let usedCodes = {};
      if (fs.existsSync(this.usedCodesFile)) {
        usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      }
      
      usedCodes[activationCode] = {
        ...data,
        used: false,
        storedAt: new Date().toISOString()
      };
      
      fs.writeFileSync(this.usedCodesFile, JSON.stringify(usedCodes, null, 2));
      return true;
    } catch (error) {
      console.error('Error storing activation data:', error);
      return false;
    }
  }

  /**
   * Get stored activation data
   */
  getStoredActivationData(activationCode) {
    try {
      if (!fs.existsSync(this.usedCodesFile)) {
        return null;
      }
      
      const usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      return usedCodes[activationCode] || null;
    } catch (error) {
      console.error('Error reading activation data:', error);
      return null;
    }
  }

  /**
   * Mark code as used
   */
  markCodeAsUsed(activationCode, usageInfo) {
    try {
      const usedCodes = JSON.parse(fs.readFileSync(this.usedCodesFile, 'utf8'));
      if (usedCodes[activationCode]) {
        usedCodes[activationCode].used = true;
        usedCodes[activationCode].usedAt = usageInfo.usedAt;
        usedCodes[activationCode].usedBy = usageInfo.usedBy;
        usedCodes[activationCode].usageDetails = usageInfo;
        
        fs.writeFileSync(this.usedCodesFile, JSON.stringify(usedCodes, null, 2));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking code as used:', error);
      return false;
    }
  }

  /**
   * Store system activation
   */
  storeSystemActivation(activationInfo) {
    try {
      fs.writeFileSync(this.activationFile, JSON.stringify(activationInfo, null, 2));
      return true;
    } catch (error) {
      console.error('Error storing system activation:', error);
      return false;
    }
  }

  /**
   * Get system activation
   */
  getSystemActivation() {
    try {
      if (!fs.existsSync(this.activationFile)) {
        return null;
      }
      return JSON.parse(fs.readFileSync(this.activationFile, 'utf8'));
    } catch (error) {
      console.error('Error reading system activation:', error);
      return null;
    }
  }
}

module.exports = BrowserIndependentActivation;
