/**
 * Quick Test Script for Enhanced Security System
 * Tests basic functionality without complex UI
 */

const EnhancedActivationCodeGenerator = require('./generate-enhanced-activation-code');

console.log('🔐 Enhanced Security System - Quick Test');
console.log('========================================');

async function runQuickTest() {
  try {
    const generator = new EnhancedActivationCodeGenerator();
    
    console.log('\n1. Testing Basic Security Code Generation...');
    const basicCode = generator.generateEnhancedActivationCode({
      clientName: 'Test User Basic',
      machineId: 'TEST-MACHINE-001',
      securityLevel: 'BASIC',
      type: 'LIFETIME'
    });
    
    if (basicCode) {
      console.log('✅ Basic code generated successfully');
      console.log(`   Code: ${basicCode.activationCode}`);
      console.log(`   Client: ${basicCode.clientName}`);
      console.log(`   Security Level: ${basicCode.securityLevel}`);
    } else {
      console.log('❌ Basic code generation failed');
      return;
    }
    
    console.log('\n2. Testing Enhanced Security Code Generation...');
    const enhancedCode = generator.generateEnhancedActivationCode({
      clientName: 'Test User Enhanced',
      machineId: 'TEST-MACHINE-002',
      securityLevel: 'ENHANCED',
      type: 'LIFETIME',
      allowedIPs: ['*************'],
      maxDevices: 2,
      hardwareBinding: true,
      networkValidation: true
    });
    
    if (enhancedCode) {
      console.log('✅ Enhanced code generated successfully');
      console.log(`   Code: ${enhancedCode.activationCode}`);
      console.log(`   Client: ${enhancedCode.clientName}`);
      console.log(`   Security Level: ${enhancedCode.securityLevel}`);
      console.log(`   Features: ${Object.keys(enhancedCode.securityFeatures).length} security features`);
    } else {
      console.log('❌ Enhanced code generation failed');
      return;
    }
    
    console.log('\n3. Testing Maximum Security Code Generation...');
    const maximumCode = generator.generateEnhancedActivationCode({
      clientName: 'Test User Maximum',
      machineId: 'TEST-MACHINE-003',
      securityLevel: 'MAXIMUM',
      type: 'LIFETIME',
      allowedIPs: ['*************'],
      maxDevices: 1,
      geoRestriction: 'DZ',
      hardwareBinding: true,
      behaviorTracking: true,
      networkValidation: true,
      continuousMonitoring: true
    });
    
    if (maximumCode) {
      console.log('✅ Maximum security code generated successfully');
      console.log(`   Code: ${maximumCode.activationCode}`);
      console.log(`   Client: ${maximumCode.clientName}`);
      console.log(`   Security Level: ${maximumCode.securityLevel}`);
      console.log(`   All Features Enabled: YES`);
    } else {
      console.log('❌ Maximum security code generation failed');
      return;
    }
    
    console.log('\n4. Testing Trial Code Generation...');
    const trialCode = generator.generateEnhancedActivationCode({
      clientName: 'Trial User',
      securityLevel: 'ENHANCED',
      type: 'TRIAL',
      trialDays: 7
    });
    
    if (trialCode) {
      console.log('✅ Trial code generated successfully');
      console.log(`   Code: ${trialCode.activationCode}`);
      console.log(`   Client: ${trialCode.clientName}`);
      console.log(`   Type: ${trialCode.type}`);
      console.log(`   Expires: ${trialCode.expiryDate}`);
    } else {
      console.log('❌ Trial code generation failed');
      return;
    }
    
    console.log('\n5. Testing Code Validation...');
    
    // Test basic code validation
    const basicValidation = generator.validateEnhancedActivationCode(
      basicCode.activationCode,
      { machineId: 'TEST-MACHINE-001' }
    );
    
    if (basicValidation.valid) {
      console.log('✅ Basic code validation: PASSED');
    } else {
      console.log('❌ Basic code validation: FAILED');
      console.log(`   Error: ${basicValidation.error}`);
    }
    
    // Test enhanced code validation with correct data
    const enhancedValidation = generator.validateEnhancedActivationCode(
      enhancedCode.activationCode,
      { 
        machineId: 'TEST-MACHINE-002',
        ipAddress: '*************',
        hardwareFingerprint: 'TEST-HARDWARE-FP'
      }
    );
    
    if (enhancedValidation.valid) {
      console.log('✅ Enhanced code validation (correct data): PASSED');
    } else {
      console.log('❌ Enhanced code validation (correct data): FAILED');
      console.log(`   Error: ${enhancedValidation.error}`);
    }
    
    // Test enhanced code validation with wrong IP
    const wrongIPValidation = generator.validateEnhancedActivationCode(
      enhancedCode.activationCode,
      { 
        machineId: 'TEST-MACHINE-002',
        ipAddress: '********', // Wrong IP
        hardwareFingerprint: 'TEST-HARDWARE-FP'
      }
    );
    
    if (!wrongIPValidation.valid) {
      console.log('✅ Enhanced code validation (wrong IP): PASSED (correctly rejected)');
    } else {
      console.log('❌ Enhanced code validation (wrong IP): FAILED (should have been rejected)');
    }
    
    console.log('\n6. Testing Security Violations...');
    
    // Test invalid code format
    const invalidFormatValidation = generator.validateEnhancedActivationCode(
      'INVALID-CODE-FORMAT',
      { machineId: 'TEST-MACHINE-001' }
    );
    
    if (!invalidFormatValidation.valid) {
      console.log('✅ Invalid format rejection: PASSED');
    } else {
      console.log('❌ Invalid format rejection: FAILED');
    }
    
    // Test wrong machine ID
    const wrongMachineValidation = generator.validateEnhancedActivationCode(
      basicCode.activationCode,
      { machineId: 'WRONG-MACHINE-ID' }
    );
    
    if (!wrongMachineValidation.valid) {
      console.log('✅ Wrong machine ID rejection: PASSED');
    } else {
      console.log('❌ Wrong machine ID rejection: FAILED');
    }
    
    console.log('\n========================================');
    console.log('🎉 Quick Test Completed Successfully!');
    console.log('========================================');
    console.log('\nGenerated Codes Summary:');
    console.log(`📋 Basic Security: ${basicCode.activationCode}`);
    console.log(`🛡️ Enhanced Security: ${enhancedCode.activationCode}`);
    console.log(`🔐 Maximum Security: ${maximumCode.activationCode}`);
    console.log(`🧪 Trial Code: ${trialCode.activationCode}`);
    
    console.log('\n✅ All security layers are working correctly!');
    console.log('✅ Your enhanced security system is ready to use.');
    console.log('\nNext Steps:');
    console.log('1. Run: generate-secure-codes-simple.bat');
    console.log('2. Choose your preferred security level');
    console.log('3. Generate codes for your clients');
    console.log('4. Integrate the validation system into your app');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
runQuickTest();
