/**
 * نافذة التفعيل - iCalDZ Accounting System
 * Activation Dialog Component
 */

import React, { useState, useEffect } from 'react';
import { activationManager } from './activation.js';
import { useLanguage } from './LanguageContext.jsx';

const ActivationDialog = ({ onActivationSuccess }) => {
  const { t, currentLanguage, getCurrentLanguageConfig } = useLanguage();
  const [activationCode, setActivationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [machineId, setMachineId] = useState('');
  const [showResetOption, setShowResetOption] = useState(false);
  const [keySequence, setKeySequence] = useState('');

  // Get language configuration
  const languageConfig = getCurrentLanguageConfig();
  const isRTL = currentLanguage === 'ar';

  useEffect(() => {
    // عرض معرف الجهاز للمساعدة في التفعيل
    const fingerprint = activationManager.generateMachineFingerprint();
    setMachineId(fingerprint);
  }, []);

  // مراقبة الضغط على المفاتيح لإظهار خيار إعادة التعيين
  useEffect(() => {
    const handleKeyPress = (e) => {
      const newSequence = keySequence + e.key.toLowerCase();
      setKeySequence(newSequence);

      // إذا تم كتابة "reset" أو "test"
      if (newSequence.includes('reset') || newSequence.includes('test')) {
        setShowResetOption(true);
        setKeySequence('');
      }

      // إعادة تعيين التسلسل بعد 3 ثوان
      setTimeout(() => {
        setKeySequence('');
      }, 3000);
    };

    document.addEventListener('keypress', handleKeyPress);
    return () => document.removeEventListener('keypress', handleKeyPress);
  }, [keySequence]);

  const handleActivation = async () => {
    if (!activationCode.trim()) {
      setError(t('pleaseEnterActivationCode'));
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = activationManager.activateProgram(activationCode.trim());

      if (result.success) {
        // نجح التفعيل - عرض رسالة مناسبة حسب نوع التفعيل
        if (result.data.type === 'TRIAL') {
          const daysLeft = result.data.trialDays;
          const expiryDate = new Date(result.data.expiryDate).toLocaleDateString('ar-DZ');
          setError(''); // مسح أي خطأ سابق
          // يمكن إضافة رسالة نجاح مخصصة للتجربة هنا
        }
        onActivationSuccess(result.data);
      } else {
        // Handle specific error messages with translations
        let errorMessage = result.error;
        if (errorMessage.includes('البرنامج مفعل بالفعل على هذا الجهاز')) {
          errorMessage = t('programAlreadyActivated');
        } else if (errorMessage.includes('تنسيق كود التفعيل غير صحيح')) {
          errorMessage = t('invalidActivationCodeFormat');
        }
        setError(errorMessage);
      }
    } catch (error) {
      setError(t('unexpectedActivationError'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    let value = e.target.value.toUpperCase();
    // إزالة الأحرف غير المسموحة
    value = value.replace(/[^A-Z0-9-]/g, '');
    setActivationCode(value);
    setError('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleActivation();
    }
  };

  const handleResetActivation = () => {
    if (window.confirm(t('confirmResetActivation'))) {
      activationManager.resetActivation();
      setError('');
      setActivationCode('');
      alert(t('resetActivationSuccess'));
      // إعادة تحميل الصفحة لإعادة عرض نافذة التفعيل
      window.location.reload();
    }
  };

  return (
    <div className="activation-overlay" dir={languageConfig.direction}>
      <div className="activation-dialog">
        <div className="activation-header">
          <div className="activation-logo">
            <h1>🏪 iCalDZ</h1>
            <p>{t('systemDescription', 'نظام المحاسبة المتكامل')}</p>
          </div>
        </div>

        <div className="activation-content">
          <h2>{t('activationTitle')}</h2>
          <p className="activation-description">
            {t('activationDescription')}
          </p>

          <div className="activation-form">
            <div className="form-group">
              <label htmlFor="activationCode">{t('activationCodeLabel')}</label>
              <input
                type="text"
                id="activationCode"
                value={activationCode}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                placeholder={t('activationCodePlaceholder')}
                className={`activation-input ${error ? 'error' : ''}`}
                disabled={isLoading}
                maxLength={50}
              />
            </div>

            {error && (
              <div className="activation-error">
                <span>⚠️ {error}</span>
              </div>
            )}

            <button
              onClick={handleActivation}
              disabled={isLoading || !activationCode.trim()}
              className="activation-button"
            >
              {isLoading ? (
                <>
                  <span className="loading-spinner">⏳</span>
                  {t('activating')}
                </>
              ) : (
                <>
                  🔑 {t('activateProgram')}
                </>
              )}
            </button>

            {showResetOption && (
              <button
                onClick={handleResetActivation}
                className="reset-button"
                title={t('resetActivationTooltip')}
              >
                🔄 {t('resetActivation')}
              </button>
            )}
          </div>

          <div className="machine-info">
            <h3>{t('deviceInfo')}</h3>
            <div className="machine-id">
              <span>{t('deviceId')} </span>
              <code>{machineId}</code>
            </div>
            <p className="machine-note">
              💡 {t('deviceNote')}
            </p>
          </div>

          <div className="activation-footer">
            <div className="contact-info">
              <h4>{t('getActivationCode')}</h4>
              <p>📞 {t('phone')} +213 551 93 05 89</p>
              <p>📧 {t('email')} <EMAIL></p>
              <p>🌐 {t('website')} www.icodedz.com</p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .activation-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          font-family: 'Cairo', sans-serif;
        }

        .activation-dialog {
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
          animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .activation-header {
          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);
          color: white;
          padding: 30px;
          text-align: center;
          border-radius: 20px 20px 0 0;
        }

        .activation-logo h1 {
          margin: 0;
          font-size: 2.5rem;
          font-weight: bold;
        }

        .activation-logo p {
          margin: 5px 0 0 0;
          opacity: 0.9;
          font-size: 1.1rem;
        }

        .activation-content {
          padding: 30px;
        }

        .activation-content h2 {
          text-align: center;
          color: #2c3e50;
          margin: 0 0 15px 0;
          font-size: 1.8rem;
        }

        .activation-description {
          text-align: center;
          color: #666;
          margin-bottom: 30px;
          line-height: 1.6;
        }

        /* Language-specific text alignment */
        [dir="rtl"] .activation-content h2,
        [dir="rtl"] .activation-description,
        [dir="rtl"] .contact-info h4,
        [dir="rtl"] .contact-info p {
          text-align: right;
        }

        [dir="ltr"] .activation-content h2,
        [dir="ltr"] .activation-description,
        [dir="ltr"] .contact-info h4,
        [dir="ltr"] .contact-info p {
          text-align: left;
        }

        /* Center alignment for titles in all languages */
        .activation-content h2,
        .contact-info h4 {
          text-align: center !important;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: #2c3e50;
        }

        /* Language-specific label alignment */
        [dir="rtl"] .form-group label {
          text-align: right;
        }

        [dir="ltr"] .form-group label {
          text-align: left;
        }

        .activation-input {
          width: 100%;
          padding: 15px;
          border: 2px solid #e0e0e0;
          border-radius: 10px;
          font-size: 14px;
          font-family: 'Courier New', monospace;
          text-align: center;
          transition: all 0.3s ease;
          box-sizing: border-box;
        }

        .activation-input:focus {
          outline: none;
          border-color: #16a085;
          box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
        }

        .activation-input.error {
          border-color: #e74c3c;
          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }

        .activation-error {
          background: #ffe6e6;
          color: #c0392b;
          padding: 12px;
          border-radius: 8px;
          margin: 15px 0;
          text-align: center;
          border: 1px solid #f5b7b1;
        }

        .activation-button {
          width: 100%;
          padding: 15px;
          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);
          color: white;
          border: none;
          border-radius: 10px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
        }

        .activation-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(22, 160, 133, 0.3);
        }

        .activation-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .reset-button {
          width: 100%;
          padding: 12px;
          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-top: 10px;
          opacity: 0.8;
        }

        .reset-button:hover {
          opacity: 1;
          transform: translateY(-1px);
          box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .loading-spinner {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .machine-info {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 10px;
          margin: 25px 0;
          border: 1px solid #e9ecef;
        }

        .machine-info h3 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 1.1rem;
        }

        /* Language-specific machine info alignment */
        [dir="rtl"] .machine-info h3,
        [dir="rtl"] .machine-id,
        [dir="rtl"] .machine-note {
          text-align: right;
        }

        [dir="ltr"] .machine-info h3,
        [dir="ltr"] .machine-id,
        [dir="ltr"] .machine-note {
          text-align: left;
        }

        .machine-id {
          background: white;
          padding: 10px;
          border-radius: 5px;
          border: 1px solid #ddd;
          margin-bottom: 10px;
        }

        .machine-id code {
          font-family: 'Courier New', monospace;
          color: #16a085;
          font-weight: bold;
        }

        .machine-note {
          font-size: 0.9rem;
          color: #666;
          margin: 0;
          font-style: italic;
        }

        .activation-footer {
          border-top: 1px solid #e9ecef;
          padding-top: 20px;
          margin-top: 20px;
        }

        .contact-info h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          text-align: center;
        }

        .contact-info p {
          margin: 8px 0;
          color: #666;
          text-align: center;
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  );
};

export default ActivationDialog;
