import React, { useState } from 'react';
import { useLanguage, LANGUAGES } from './LanguageContext.jsx';

const LanguageSelectionDialog = ({ onLanguageSelected }) => {
  const { changeLanguage, t } = useLanguage();
  const [selectedLang, setSelectedLang] = useState('ar');
  const [isSelecting, setIsSelecting] = useState(false);

  const handleLanguageSelect = async (langCode) => {
    setIsSelecting(true);
    
    try {
      // Change language
      const success = changeLanguage(langCode);
      
      if (success) {
        // Small delay for visual feedback
        setTimeout(() => {
          setIsSelecting(false);
          if (onLanguageSelected) {
            onLanguageSelected(langCode);
          }
        }, 500);
      } else {
        setIsSelecting(false);
      }
    } catch (error) {
      console.error('Error selecting language:', error);
      setIsSelecting(false);
    }
  };

  return (
    <div className="language-selection-overlay">
      <div className="language-selection-dialog">
        <div className="language-header">
          <div className="language-icon">🌐</div>
          <h2>اختر اللغة / Choisir la langue / Select Language</h2>
          <p>يرجى اختيار لغة التطبيق / Veuillez choisir la langue / Please select your language</p>
        </div>

        <div className="language-options">
          {Object.entries(LANGUAGES).map(([code, config]) => (
            <button
              key={code}
              className={`language-option ${selectedLang === code ? 'selected' : ''}`}
              onClick={() => setSelectedLang(code)}
              disabled={isSelecting}
            >
              <div className="language-flag">
                {code === 'ar' && '🇩🇿'}
                {code === 'fr' && '🇫🇷'}
                {code === 'en' && '🇺🇸'}
              </div>
              <div className="language-info">
                <span className="language-name">{config.name}</span>
                <span className="language-direction">
                  {code === 'ar' && 'من اليمين إلى اليسار'}
                  {code === 'fr' && 'De gauche à droite'}
                  {code === 'en' && 'Left to Right'}
                </span>
              </div>
              {selectedLang === code && (
                <div className="language-check">✓</div>
              )}
            </button>
          ))}
        </div>

        <div className="language-actions">
          <button
            className="confirm-language-btn"
            onClick={() => handleLanguageSelect(selectedLang)}
            disabled={isSelecting}
          >
            {isSelecting ? (
              <span className="loading-spinner">⏳</span>
            ) : (
              <>
                {selectedLang === 'ar' && 'تأكيد الاختيار'}
                {selectedLang === 'fr' && 'Confirmer la sélection'}
                {selectedLang === 'en' && 'Confirm Selection'}
              </>
            )}
          </button>
        </div>

        <div className="language-footer">
          <small>
            يمكنك تغيير اللغة لاحقاً من الإعدادات<br/>
            Vous pouvez changer la langue plus tard dans les paramètres<br/>
            You can change the language later in settings
          </small>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelectionDialog;
